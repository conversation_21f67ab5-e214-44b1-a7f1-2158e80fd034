//
//  CStoryApp.swift
//  CStory
//
//  Created by NZUE on 2025/6/10.
//

import SwiftData
import SwiftUI

@main
struct CStoryApp: App {
  var sharedModelContainer: ModelContainer = {
    let schema = Schema([
      CardModel.self,
      TransactionModel.self,
      TransactionMainCategoryModel.self,
      TransactionSubCategoryModel.self,
      CurrencyModel.self,
      ChatMessageModel.self,
    ])
    let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)

    do {
      return try ModelContainer(for: schema, configurations: [modelConfiguration])
    } catch {
      fatalError("无法创建ModelContainer: \(error)")
    }
  }()

  var body: some Scene {
    WindowGroup {
      ContentView()
        .onAppear {
          initializeAppDataIfNeeded()
        }

    }
    .modelContainer(sharedModelContainer)

  }

  private func initializeAppDataIfNeeded() {
    print("检查应用数据初始化状态...")

    let localDataInitialized = "com.cstory.localDataInitialized"
    let cloudDataInitialized = "com.cstory.cloudDataInitialized"

    // 首先检查本地是否已初始化过
    if UserDefaults.standard.bool(forKey: localDataInitialized) {
      print("检测到本地已初始化数据，跳过初始化流程")
      return
    }

    // 本地未初始化，检查云端是否已初始化数据
    print("本地未初始化，检查云端初始化状态...")
    NSUbiquitousKeyValueStore.default.synchronize()

    if NSUbiquitousKeyValueStore.default.bool(forKey: cloudDataInitialized) {
      print("检测到云端已初始化数据，标记本地已初始化并跳过初始化流程")
      // 标记本地已初始化，避免下次再检查云端
      UserDefaults.standard.set(true, forKey: localDataInitialized)
      return
    }

    // 云端和本地都未初始化，执行初始化流程
    print("开始执行数据初始化流程...")
    initTransactionCategories()
    initCurrencies()

    // 标记本地和云端都已初始化
    UserDefaults.standard.set(true, forKey: localDataInitialized)
    NSUbiquitousKeyValueStore.default.set(true, forKey: cloudDataInitialized)
    NSUbiquitousKeyValueStore.default.synchronize()
    print("数据初始化完成，已标记本地和云端初始化状态")

  }
  /// 初始化交易类别数据
  @MainActor private func initTransactionCategories() {
    print("🚀 开始初始化交易类别数据...")

    // 创建ModelContext用于数据初始化
    let context = ModelContext(sharedModelContainer)

    // 从本地JSON加载默认类别数据
    let mainCategories = MainCategoryJSONDecoder.decode(from: "TransactionCategoryDefaults")
    guard !mainCategories.isEmpty else {
      print("❌ 本地JSON文件为空或解析失败，无法创建类别")
      return
    }

    print("📊 从JSON加载了\(mainCategories.count)个主分类，开始创建类别并将图片存储到数据库...")

    // 创建主类别和子类别
    var totalSubCategories = 0
    for item in mainCategories {
      // 创建主类别
      let mainCategory = TransactionMainCategoryModel(
        id: item.categoryId,
        name: item.name,
        icon: item.iconType,
        order: item.order,
        type: item.transactionType
      )
      context.insert(mainCategory)

      // 创建子类别
      if let subCategories = item.subCategories {
        print("为主类别 '\(item.name)' 创建\(subCategories.count)个子类别")
        totalSubCategories += subCategories.count

        subCategories.forEach { subItem in
          let subCategory = TransactionSubCategoryModel(
            id: subItem.categoryId,
            name: subItem.name,
            icon: subItem.iconType,
            order: subItem.order,
            mainId: mainCategory.id
          )
          context.insert(subCategory)
          // SwiftData会自动处理@Relationship，只需设置一侧
          subCategory.mainCategory = mainCategory
        }
      }
    }
    print("✅ 交易类别创建完成:")
    print("   📁 主分类: \(mainCategories.count) 个")
    print("   📄 子分类: \(totalSubCategories) 个")

    print("🎉 交易类别初始化完成！")
  }
  @MainActor private func initCurrencies() {
    print("🚀 开始初始化货币数据...")

    // 创建ModelContext用于数据初始化
    let context = ModelContext(sharedModelContainer)

    // 获取货币名称和符号映射
    let (currencyNames, currencySymbols) = CurrencyService.getCurrencyMappings()

    // 从本地JSON文件读取数据
    guard let fileURL = Bundle.main.url(forResource: "exchange_rates", withExtension: "json") else {
      print("错误：无法找到exchange_rates.json文件，初始化货币数据失败")
      return
    }

    do {
      print("开始从本地JSON文件读取货币数据...")
      // 读取文件内容
      let jsonData = try Data(contentsOf: fileURL)

      // 解析JSON数据
      let decoder = JSONDecoder()
      let exchangeRates = try decoder.decode(ExchangeRateAPIResponse.self, from: jsonData)

      print("成功从本地JSON获取到\(exchangeRates.data.rates.count)种货币数据，开始插入数据库")

      let startTime = Date()

      // 货币排序计数器
      var orderCounter = 0

      // 优先添加的常用货币代码列表（按照显示顺序排列）
      let priorityCurrencies = CurrencyService.getPriorityCurrencies()

      // 创建一个处理过的货币代码集合，用于跟踪已处理的货币
      var processedCurrencies = Set<String>()

      // 先添加人民币作为基准货币
      let cny = CurrencyModel(
        name: "人民币",
        code: "CNY",
        symbol: "¥",
        rate: 1.0,
        defaultRate: 1.0,  // 设置默认汇率
        isBaseCurrency: true,
        isCustom: false,
        isSelected: true,
        order: orderCounter
      )
      orderCounter += 1
      context.insert(cny)
      processedCurrencies.insert("CNY")

      // 将人民币设置为默认本位币
      CurrencyService.shared.setBaseCurrencyCode("CNY")

      // 按优先顺序添加其他常用货币
      for code in priorityCurrencies where code != "CNY" {
        if let apiRate = exchangeRates.data.rates[code] {
          let name = currencyNames[code] ?? code
          let symbol = currencySymbols[code] ?? code

          // API返回的汇率是"1CNY=多少外币"的形式
          // 但我们需要"1外币=多少CNY"的形式，所以需要取倒数
          var inverseRate = 1.0
          if apiRate > 0 {
            inverseRate = 1.0 / apiRate
            // 确保保留6位小数精度
            inverseRate = (inverseRate * 1_000_000).rounded() / 1_000_000
          }

          let currency = CurrencyModel(
            name: name,
            code: code,
            symbol: symbol,
            rate: inverseRate,  // 存储"1外币=多少CNY"的反向汇率
            defaultRate: inverseRate,  // 设置默认汇率
            isBaseCurrency: false,
            isCustom: false,
            isSelected: true,
            order: orderCounter
          )
          orderCounter += 1
          context.insert(currency)
          processedCurrencies.insert(code)
        }
      }

      // 然后添加所有其他货币（按字母顺序）
      let sortedRemainingCurrencies = exchangeRates.data.rates.keys.sorted().filter {
        !processedCurrencies.contains($0)
      }

      for code in sortedRemainingCurrencies {
        if let apiRate = exchangeRates.data.rates[code] {
          let name = currencyNames[code] ?? code
          let symbol = currencySymbols[code] ?? code

          // 将API返回的汇率转换为反向汇率
          var inverseRate = 1.0
          if apiRate > 0 {
            inverseRate = 1.0 / apiRate
            // 确保保留6位小数精度
            inverseRate = (inverseRate * 1_000_000).rounded() / 1_000_000
          }

          let currency = CurrencyModel(
            name: name,
            code: code,
            symbol: symbol,
            rate: inverseRate,  // 存储"1外币=多少CNY"的反向汇率
            defaultRate: inverseRate,  // 设置默认汇率
            isBaseCurrency: false,
            isCustom: false,
            isSelected: true,  // 默认所有货币都是选择状态
            order: orderCounter
          )
          orderCounter += 1
          context.insert(currency)
        }
      }

      let endTime = Date()
      let timeInterval = endTime.timeIntervalSince(startTime)
      print("✅ 货币数据初始化完成:")
      print("   💰 货币总数: \(orderCounter) 种")
      print("   ⏱️ 耗时: \(String(format: "%.2f", timeInterval))秒")
      print("   🎯 已设置人民币(CNY)为默认本位币")
    } catch {
      print("❌ 错误：读取或解析本地JSON数据失败: \(error.localizedDescription)")
      print("货币数据初始化失败")
    }
  }

}
