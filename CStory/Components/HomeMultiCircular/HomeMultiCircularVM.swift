//
//  HomeMultiCircularVM.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/21.
//

import SwiftUI
import Combine

// MARK: - 圆环数据模型

/// 圆环数据模型
public struct RingData: Identifiable {
  /// 圆环唯一标识符
  public let id = UUID()

  /// 进度值 (0.0 - 1.0)
  public let progress: Double

  /// 圆环颜色
  public let color: Color

  /// 线条宽度
  public let lineWidth: CGFloat

  /// 圆环标题（可选）
  public let title: String?

  /// 初始化圆环数据
  /// - Parameters:
  ///   - progress: 进度值，自动限制在0.0-1.0范围内
  ///   - color: 圆环颜色
  ///   - lineWidth: 线条宽度，默认8
  ///   - title: 圆环标题
  public init(progress: Double, color: Color, lineWidth: CGFloat = 8, title: String? = nil) {
    self.progress = max(0.0, min(1.0, progress))
    self.color = color
    self.lineWidth = lineWidth
    self.title = title
  }
}

// MARK: - 视图模型

/// 主页多圆环进度视图模型
///
/// 负责管理主页多圆环进度的数据和业务逻辑，遵循MVVM架构模式。
/// 该类处理圆环数据的计算、格式化和状态管理。
///
/// ## 主要职责
/// - 圆环数据管理和格式化
/// - 进度值计算和验证
/// - 动画状态管理
/// - 圆环大小和间距计算
///
/// ## 使用示例
/// ```swift
/// let viewModel = HomeMultiCircularVM(
///   rings: [
///     RingData(progress: 0.75, color: .blue, title: "收入"),
///     RingData(progress: 0.60, color: .green, title: "支出"),
///     RingData(progress: 0.40, color: .orange, title: "余额")
///   ]
/// )
/// ```
final class HomeMultiCircularVM: ObservableObject {

  // MARK: - Published Properties

  /// 圆环数据数组
  @Published var rings: [RingData]

  /// 整体大小
  @Published var size: CGFloat

  /// 圆环间距
  @Published var spacing: CGFloat

  /// 是否启用动画
  @Published var isAnimationEnabled: Bool

  /// 动画持续时间
  @Published var animationDuration: Double

  /// 动画延迟间隔
  @Published var animationDelay: Double

  // MARK: - Initialization

  /// 初始化多圆环进度视图模型
  /// - Parameters:
  ///   - rings: 圆环数据数组，最多支持3个圆环
  ///   - size: 整体大小，默认90
  ///   - spacing: 圆环间距，默认4
  ///   - isAnimationEnabled: 是否启用动画，默认true
  ///   - animationDuration: 动画持续时间，默认0.8秒
  ///   - animationDelay: 动画延迟间隔，默认0.15秒
  init(
    rings: [RingData],
    size: CGFloat = 90,
    spacing: CGFloat = 4,
    isAnimationEnabled: Bool = true,
    animationDuration: Double = 0.8,
    animationDelay: Double = 0.15
  ) {
    // 确保最多3个圆环
    self.rings = Array(rings.prefix(3))
    self.size = size
    self.spacing = spacing
    self.isAnimationEnabled = isAnimationEnabled
    self.animationDuration = animationDuration
    self.animationDelay = animationDelay
  }

  /// 便利初始化器 - 使用默认样式创建三个圆环
  /// - Parameters:
  ///   - outerProgress: 外圈进度，默认0.75
  ///   - middleProgress: 中圈进度，默认0.6
  ///   - innerProgress: 内圈进度，默认0.4
  ///   - size: 整体大小，默认90
  convenience init(
    outerProgress: Double = 0.75,
    middleProgress: Double = 0.6,
    innerProgress: Double = 0.4,
    size: CGFloat = 90
  ) {
    self.init(
      rings: [
        RingData(progress: outerProgress, color: .accentColor, lineWidth: 8, title: "外环"),
        RingData(progress: middleProgress, color: .green, lineWidth: 8, title: "中环"),
        RingData(progress: innerProgress, color: .orange, lineWidth: 8, title: "内环")
      ],
      size: size
    )
  }

  // MARK: - Public Methods

  /// 更新圆环进度
  /// - Parameters:
  ///   - index: 圆环索引
  ///   - progress: 新的进度值
  func updateProgress(at index: Int, to progress: Double) {
    guard index < rings.count else { return }

    let updatedRing = RingData(
      progress: progress,
      color: rings[index].color,
      lineWidth: rings[index].lineWidth,
      title: rings[index].title
    )

    rings[index] = updatedRing
  }

  /// 更新所有圆环进度
  /// - Parameter progresses: 进度值数组
  func updateAllProgresses(_ progresses: [Double]) {
    for (index, progress) in progresses.enumerated() {
      updateProgress(at: index, to: progress)
    }
  }

  /// 计算指定索引圆环的大小
  /// - Parameter index: 圆环索引
  /// - Returns: 圆环大小
  func ringSize(at index: Int) -> CGFloat {
    guard index < rings.count else { return 0 }
    let ring = rings[index]
    return size - CGFloat(index) * (ring.lineWidth + spacing) * 2
  }

  /// 计算动画延迟时间
  /// - Parameter index: 圆环索引
  /// - Returns: 延迟时间
  func animationDelay(for index: Int) -> Double {
    return isAnimationEnabled ? Double(index) * animationDelay : 0
  }

  /// 获取动画配置
  /// - Parameter index: 圆环索引
  /// - Returns: SwiftUI Animation
  func animation(for index: Int) -> Animation {
    guard isAnimationEnabled else { return .easeInOut(duration: 0) }
    return .easeInOut(duration: animationDuration)
      .delay(animationDelay(for: index))
  }

  // MARK: - Data Validation

  /// 验证进度值是否有效
  /// - Parameter progress: 进度值
  /// - Returns: 是否有效
  static func isValidProgress(_ progress: Double) -> Bool {
    return progress >= 0.0 && progress <= 1.0
  }

  /// 标准化进度值到有效范围
  /// - Parameter progress: 原始进度值
  /// - Returns: 标准化后的进度值
  static func normalizeProgress(_ progress: Double) -> Double {
    return max(0.0, min(1.0, progress))
  }
}

// MARK: - Factory Methods

extension HomeMultiCircularVM {

  /// 创建资产统计样式的多圆环进度
  /// - Parameters:
  ///   - totalAsset: 总资产进度
  ///   - totalLiability: 总负债进度
  ///   - netAsset: 净资产进度
  ///   - size: 大小
  /// - Returns: 配置好的视图模型
  static func assetStatistics(
    totalAsset: Double,
    totalLiability: Double,
    netAsset: Double,
    size: CGFloat = 90
  ) -> HomeMultiCircularVM {
    return HomeMultiCircularVM(
      rings: [
        RingData(progress: totalAsset, color: .blue, lineWidth: 8, title: "总资产"),
        RingData(progress: totalLiability, color: .red, lineWidth: 8, title: "总负债"),
        RingData(progress: netAsset, color: .green, lineWidth: 8, title: "净资产")
      ],
      size: size
    )
  }

  /// 创建收支统计样式的多圆环进度
  /// - Parameters:
  ///   - income: 收入进度
  ///   - expense: 支出进度
  ///   - balance: 余额进度
  ///   - size: 大小
  /// - Returns: 配置好的视图模型
  static func incomeExpenseStatistics(
    income: Double,
    expense: Double,
    balance: Double,
    size: CGFloat = 90
  ) -> HomeMultiCircularVM {
    return HomeMultiCircularVM(
      rings: [
        RingData(progress: income, color: .green, lineWidth: 8, title: "收入"),
        RingData(progress: expense, color: .red, lineWidth: 8, title: "支出"),
        RingData(progress: balance, color: .blue, lineWidth: 8, title: "余额")
      ],
      size: size
    )
  }

  /// 创建默认样式的多圆环进度
  /// - Parameter size: 大小
  /// - Returns: 配置好的视图模型
  static func `default`(size: CGFloat = 90) -> HomeMultiCircularVM {
    return HomeMultiCircularVM(size: size)
  }
}
