//
//  Card.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/18.
//

import SwiftUI

/// 卡片组件
///
/// 这是一个纯粹的UI组件，它接收一个`CardVM`对象并将其数据显示出来。
/// 所有的业务逻辑、数据格式化均由VM处理。
/// 参考现有CardComponentView的设计，保持一致的样式。
struct Card: View {

  // MARK: - 属性

  // View只依赖于VM，不再关心原始数据
  @ObservedObject var viewModel: CardVM

  /// 数据管理器
  @Environment(\.dataManager) private var dataManager

  // MARK: - 主体视图

  var body: some View {
    Group {
      if viewModel.onTap != nil {
        // 可点击版本
        Button {
          dataManager.hapticManager.trigger(.impactMedium)
          viewModel.onTap?()
        } label: {
          cardContent
        }
      } else {
        // 纯展示版本
        cardContent
      }
    }
  }

  // MARK: - 卡片内容

  private var cardContent: some View {
    VStack(spacing: 26) {
      // MARK: 顶部区域：银行名称和图标
      HStack(spacing: 4) {
        Text(viewModel.displayBankName)
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(viewModel.textColor)
        Spacer()

        // 银行logo
        IconView(
          viewModel: IconViewVM.optionalImage(
            viewModel.bankLogo,
            size: 24,
            style: IconStyle(
              backgroundColor: Color.cWhite,
              cornerRadius: 6
            )
          )
        )
      }

      // MARK: 中间区域：金额显示
      VStack(alignment: .leading, spacing: 4) {
        // 根据金额正负值判断显示文案
        let isPositiveBalance = viewModel.balance >= 0
        Text(isPositiveBalance ? "当前余额" : "当前欠款")
          .font(.system(size: 15, weight: .medium))
          .foregroundColor(viewModel.textColor.opacity(0.8))

        HStack {
          DisplayCurrencyView.size32(
            symbol: viewModel.currencySymbol,
            amount: viewModel.balance
          )
          .color(viewModel.textColor)
          .foregroundColor(viewModel.textColor)
          Spacer()
        }
      }

      // MARK: 底部区域：日期信息和卡片名称
      HStack(spacing: 4) {
        // 信用卡特有的出账日/还款日信息
        if viewModel.isCredit && (viewModel.billDay != nil || viewModel.dueDay != nil) {
          // 确定显示出账日还是还款日
          let shouldShowDueDate = viewModel.shouldShowBillDate == false

          if let targetDate = shouldShowDueDate
            ? viewModel.getNextDueDate?() : viewModel.getNextBillDate?()
          {
            let days = viewModel.getDaysUntilDate(targetDate)

            Text(shouldShowDueDate ? "距离还款日" : "距离出账日")
              .font(.system(size: 14))
              .foregroundColor(viewModel.textColor.opacity(0.8))

            // 始终显示天数，不使用"今日到期"
            Text("\(days)天")
              .font(.system(size: 14, weight: .regular))
              .foregroundColor(
                days <= 7 ? Color.cAccentRed : viewModel.textColor.opacity(0.8)
              )
          }
        }
        Spacer()
        Text(viewModel.cardName.isEmpty ? "未命名卡片" : viewModel.cardName)
          .font(.system(size: 14, weight: .regular))
          .foregroundColor(viewModel.textColor.opacity(0.8))
      }
    }
    .padding(16)
    .frame(height: 190)
    .background(
      viewModel.cardBackground
    )
    .cornerRadius(24)
    .padding(.horizontal, 16)
  }
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct Card_Previews: PreviewProvider {
    static var previews: some View {
      ScrollView {
        VStack(spacing: 16) {
          Text("Card 组件预览").font(.largeTitle).bold().padding()

          VStack(spacing: -24) {
            // 储蓄卡示例
            Card(
              viewModel: CardVM(
                bankName: "招商银行",
                cardName: "储蓄卡",
                balance: 8580.50,
                currencySymbol: "¥",
                isCredit: false,
                onTap: { print("储蓄卡被点击") }
              ))

            // 信用卡示例（带出账日/还款日）
            Card(
              viewModel: CardVM(
                bankName: "中国工商银行",
                cardName: "信用卡",
                balance: -2500.00,
                currencySymbol: "¥",
                isCredit: true,
                billDay: 15,
                dueDay: 25,
                onTap: { print("信用卡被点击") },
                getNextBillDate: {
                  // 模拟下一个出账日：本月15日或下月15日
                  let calendar = Calendar.current
                  let today = Date()
                  let components = calendar.dateComponents([.year, .month], from: today)
                  var billComponents = components
                  billComponents.day = 15

                  if let thisMonthBill = calendar.date(from: billComponents),
                    thisMonthBill > today
                  {
                    return thisMonthBill
                  } else {
                    billComponents.month = (components.month ?? 1) + 1
                    return calendar.date(from: billComponents)
                  }
                },
                getNextDueDate: {
                  // 模拟下一个还款日：本月25日或下月25日
                  let calendar = Calendar.current
                  let today = Date()
                  let components = calendar.dateComponents([.year, .month], from: today)
                  var dueComponents = components
                  dueComponents.day = 25

                  if let thisMonthDue = calendar.date(from: dueComponents),
                    thisMonthDue > today
                  {
                    return thisMonthDue
                  } else {
                    dueComponents.month = (components.month ?? 1) + 1
                    return calendar.date(from: dueComponents)
                  }
                }
              ))

            // 支付宝余额示例
            Card(
              viewModel: CardVM(
                bankName: "支付宝",
                cardName: "余额",
                balance: 1024.88,
                currencySymbol: "¥",
                isCredit: false,
                onTap: { print("支付宝被点击") }
              ))
          }
        }

      }
      .background(Color.cLightBlue)
    }
  }
#endif
