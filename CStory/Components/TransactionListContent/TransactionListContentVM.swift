//
//  TransactionListContentVM.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/21.
//

import Combine
import SwiftUI

/// 交易列表内容视图模型
///
/// 负责管理交易列表的显示数据，包括交易分组、货币符号等。
/// 所有的业务逻辑、数据格式化均由VM处理。
class TransactionListContentVM: ObservableObject {

  // MARK: - Output Properties (for the View to use)

  /// 交易日期分组数据
  @Published var transactionDayGroups: [TransactionDayGroupWithRowVM]

  /// 货币符号
  @Published var currencySymbol: String

  // MARK: - 初始化方法

  init(
    transactionDayGroups: [TransactionDayGroupWithRowVM],
    currencySymbol: String
  ) {
    self.transactionDayGroups = transactionDayGroups
    self.currencySymbol = currencySymbol
  }

  // MARK: - 公共方法

  /// 更新交易数据
  func updateTransactionData(
    transactionDayGroups: [TransactionDayGroupWithRowVM],
    currencySymbol: String
  ) {
    self.transactionDayGroups = transactionDayGroups
    self.currencySymbol = currencySymbol
  }

}
