//
//  SegmentedSelectorVM.swift
//  CStory
//
//  Created by AI Assistant on 2025/8/1.
//

import SwiftUI

/// 分段选择器的ViewModel
@MainActor
class SegmentedSelectorVM: ObservableObject {
  
  // MARK: - Published Properties
  
  /// 当前选中的选项ID
  @Published var selectedOptionId: String
  
  /// 当前按压的选项ID
  @Published var pressingOptionId: String?
  
  // MARK: - Properties
  
  /// 选项列表
  let options: [SegmentedOption]
  
  /// 选项改变时的回调
  private let onSelectionChanged: ((String) -> Void)?
  
  /// 触觉反馈管理器
  private let hapticManager = HapticFeedbackManager.shared
  
  // MARK: - Initialization
  
  /// 初始化ViewModel
  /// - Parameters:
  ///   - options: 选项列表
  ///   - selectedOptionId: 当前选中的选项ID
  ///   - onSelectionChanged: 选项改变时的回调
  init(
    options: [SegmentedOption],
    selectedOptionId: String,
    onSelectionChanged: ((String) -> Void)? = nil
  ) {
    self.options = options
    self.selectedOptionId = selectedOptionId
    self.onSelectionChanged = onSelectionChanged
  }
  
  // MARK: - Public Methods
  
  /// 选择选项
  /// - Parameter option: 要选择的选项
  func selectOption(_ option: SegmentedOption) {
    guard selectedOptionId != option.id else { return }
    
    // 触觉反馈
    hapticManager.trigger(.selection)
    
    // 动画切换
    withAnimation(.easeInOut(duration: 0.2)) {
      selectedOptionId = option.id
    }
    
    // 回调通知
    onSelectionChanged?(option.id)
  }
  
  /// 检查选项是否被选中
  /// - Parameter option: 要检查的选项
  /// - Returns: 是否被选中
  func isSelected(_ option: SegmentedOption) -> Bool {
    return selectedOptionId == option.id
  }
}

// MARK: - SegmentedOption

/// 分段选择器选项
struct SegmentedOption: Identifiable {
  let id: String
  let title: String
  
  init(id: String, title: String) {
    self.id = id
    self.title = title
  }
}
