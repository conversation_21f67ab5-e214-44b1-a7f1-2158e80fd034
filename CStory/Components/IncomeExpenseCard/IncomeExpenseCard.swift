//
//  IncomeExpenseCard.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/18.
//

import SwiftUI

/// 收入支出卡片组件
///
/// 显示收入和支出的统计信息，适用于卡片详情页面等场景
///
/// ## 使用示例
/// ```swift
/// // 方式1: 使用预先准备好的ViewModel（推荐）
/// let viewModel = IncomeExpenseCardVM(
///     income: 2500.00,
///     expense: 1200.50,
///     currencySymbol: "¥"
/// )
/// IncomeExpenseCard(viewModel: viewModel)
///
/// // 方式2: 直接传入数据参数
/// IncomeExpenseCard(
///     income: 2500.00,
///     expense: 1200.50,
///     currencySymbol: "¥"
/// )
/// ```
struct IncomeExpenseCard: View {

  // MARK: - ViewModel

  /// 收入支出卡片视图模型
  @ObservedObject var viewModel: IncomeExpenseCardVM

  // MARK: - 初始化

  /// 使用预先准备好的ViewModel初始化（推荐使用）
  init(viewModel: IncomeExpenseCardVM) {
    self.viewModel = viewModel
  }

  /// 直接使用数据参数初始化
  init(income: Double, expense: Double, currencySymbol: String) {
    self.viewModel = IncomeExpenseCardVM(
      income: income,
      expense: expense,
      currencySymbol: currencySymbol
    )
  }

  var body: some View {
    HStack(spacing: 12) {
      // 收入卡片
      SummaryItemCard(
        title: "收入",
        amount: viewModel.income,
        currencySymbol: viewModel.currencySymbol,
        iconName: "arrow.down.right",
        iconColor: Color.cAccentGreen
      )

      // 支出卡片
      SummaryItemCard(
        title: "支出",
        amount: viewModel.expense,
        currencySymbol: viewModel.currencySymbol,
        iconName: "arrow.up.right",
        iconColor: Color.cAccentRed
      )
    }
  }
}

/// 单个统计项卡片
private struct SummaryItemCard: View {
  let title: String
  let amount: Double
  let currencySymbol: String
  let iconName: String
  let iconColor: Color

  var body: some View {
    VStack(alignment: .leading, spacing: 8) {
      HStack {
        Image(systemName: iconName)
          .foregroundStyle(Color.cWhite)
          .font(.system(size: 12, weight: .semibold))
          .frame(width: 16, height: 16)
          .background(iconColor)
          .cornerRadius(6)
        Spacer()
      }

      VStack(alignment: .leading, spacing: 4) {
        Text(title)
          .font(.system(size: 12, weight: .medium))
          .foregroundColor(.cBlack.opacity(0.6))

        DisplayCurrencyView.size18(
          symbol: currencySymbol,
          amount: amount
        )
      }
    }
    .padding(12)
    .background(Color.cWhite)
    .cornerRadius(16)
    .overlay(
      RoundedRectangle(cornerRadius: 16)
        .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
  }
}

// MARK: - Preview

#Preview {
  VStack(spacing: 16) {
    // 使用新的便利初始化方法
    IncomeExpenseCard(
      income: 2500.00,
      expense: 1200.50,
      currencySymbol: "¥"
    )

    IncomeExpenseCard(
      income: 0.00,
      expense: 890.00,
      currencySymbol: "$"
    )

    // 使用预先准备好的ViewModel的方式
    IncomeExpenseCard(
      viewModel: IncomeExpenseCardVM(
        income: 1500.25,
        expense: 0.00,
        currencySymbol: "€"
      )
    )
  }
  .padding()
  .background(Color.cLightBlue)
}
