//
//  HomeStatisticCardVM.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/21.
//

import Foundation

/// 统计卡片视图模型
class HomeStatisticCardVM: ObservableObject {

  // MARK: - Published Properties

  @Published var title: String
  @Published var amount: Double
  @Published var currencySymbol: String
  @Published var iconName: String?

  // MARK: - Initializer

  init(
    title: String,
    amount: Double,
    currencySymbol: String = "¥",
    iconName: String? = nil
  ) {
    self.title = title
    self.amount = amount
    self.currencySymbol = currencySymbol
    self.iconName = iconName
  }

}
