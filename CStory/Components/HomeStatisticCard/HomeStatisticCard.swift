//
//  HomeStatisticCard.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 统计卡片组件
///
/// 用于展示各类统计数据的卡片，如总资产、总负债、月度收支等。
/// 支持标题、金额显示，可选图标，自动处理金额格式化。
///
/// ## 使用示例
/// ```swift
/// // 使用ViewModel
/// let viewModel = HomeStatisticCardVM(
///     title: "总资产",
///     amount: 15234.67,
///     currencySymbol: "¥",
///     iconName: "TotalCardsIcon"
/// )
/// HomeStatisticCard(viewModel: viewModel)
/// ```
struct HomeStatisticCard: View {
  // MARK: - ViewModel

  /// 统计卡片视图模型
  @ObservedObject var viewModel: HomeStatisticCardVM

  // MARK: - 初始化

  init(viewModel: HomeStatisticCardVM) {
    self.viewModel = viewModel
  }

  // MARK: - 视图主体

  var body: some View {
    VStack(alignment: .leading, spacing: 2) {
      // 标题和可选图标
      HStack(spacing: 4) {
        // 可选图标
        if let iconName = viewModel.iconName {
          Image(iconName)
            .resizable()
            .frame(width: 24, height: 24)
            .foregroundColor(.cBlack.opacity(0.6))
        }

        Text(viewModel.title)
          .font(.system(size: 13, weight: .medium))
          .foregroundColor(.cBlack.opacity(0.6))
          .frame(maxWidth: .infinity, alignment: .leading)
      }

      // 金额显示 - 使用中等尺寸的货币显示
      DisplayCurrencyView.size18(
        symbol: viewModel.currencySymbol,
        amount: viewModel.amount
      )

    }
    .padding(.horizontal, 12)
    .padding(.vertical, 8)
    .frame(maxWidth: .infinity, maxHeight: .infinity)  // 填满父视图
    .background(Color.cWhite)
    .cornerRadius(16)
    .overlay(
      RoundedRectangle(cornerRadius: 16)
        .strokeBorder(Color.accentColor.opacity(0.08), lineWidth: 1)
    )
  }
}

// MARK: - 预览

#Preview {
  VStack(spacing: 12) {
    // 带图标的卡片
    HomeStatisticCard(
      viewModel: HomeStatisticCardVM(
        title: "总资产",
        amount: 15234.67,
        currencySymbol: "¥",
        iconName: "TotalCardsIcon"
      )
    )

    // 不带图标的卡片
    HomeStatisticCard(
      viewModel: HomeStatisticCardVM(
        title: "总负债",
        amount: 3456.78,
        currencySymbol: "¥"
      )
    )
  }
  .padding()
  .background(Color.gray.opacity(0.1))
}
