//
//  SegmentedSelector.swift
//  CStory
//
//  Created by AI Assistant on 2025/8/1.
//

import SwiftUI

/// 通用分段选择器组件
///
/// 提供两个或多个选项之间的切换功能，支持：
/// - 动画切换效果
/// - 触觉反馈
/// - 按压动画
struct SegmentedSelector: View {

  // MARK: - Properties

  /// ViewModel
  @ObservedObject private var viewModel: SegmentedSelectorVM

  /// 动画命名空间
  @Namespace private var animation

  // MARK: - Initialization

  /// 初始化分段选择器
  /// - Parameter viewModel: 分段选择器的ViewModel
  init(viewModel: SegmentedSelectorVM) {
    self.viewModel = viewModel
  }

  // MARK: - Body

  var body: some View {
    HStack(spacing: 8) {
      ForEach(viewModel.options, id: \.id) { option in
        optionButton(for: option)
      }
    }
    .padding(.horizontal, 2)
    .padding(.vertical, 2)
    .background(Color.cWhite)
    .cornerRadius(16)
    .overlay(
      RoundedRectangle(cornerRadius: 16)
        .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
    .padding(.horizontal, 16)
    .padding(.vertical, 12)
  }

  // MARK: - Private Methods

  /// 创建选项按钮
  private func optionButton(for option: SegmentedOption) -> some View {
    Button {
      viewModel.selectOption(option)
    } label: {
      Text(option.title)
        .font(.system(size: 16, weight: .medium))
        .foregroundColor(
          viewModel.isSelected(option) ? Color.cWhite : Color.cBlack.opacity(0.6)
        )
        .frame(maxWidth: .infinity)
        .frame(height: 34)
        .background(
          ZStack {
            if viewModel.isSelected(option) {
              Color.cAccentBlue
                .cornerRadius(14)
                .matchedGeometryEffect(id: "background", in: animation)
            }
          }
        )
        .scaleEffect(viewModel.pressingOptionId == option.id ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: viewModel.pressingOptionId)
    }
    .onLongPressGesture(
      minimumDuration: 0, maximumDistance: .infinity,
      pressing: { pressing in
        if pressing {
          viewModel.pressingOptionId = option.id
        } else {
          viewModel.pressingOptionId = nil
        }
      }, perform: {})
  }
}

// MARK: - TransactionType Extension

extension TransactionType {
  /// 显示名称
  var displayName: String {
    switch self {
    case .expense:
      return "支出"
    case .income:
      return "收入"
    case .transfer:
      return "转账"
    case .refund:
      return "退款"
    case .createCard:
      return "创建卡片"
    case .adjustCard:
      return "调整余额"
    }
  }
}

// MARK: - Convenience Extensions

extension SegmentedSelector {
  /// 创建交易类型选择器
  static func transactionType(
    selectedType: TransactionType,
    onTypeChanged: ((TransactionType) -> Void)? = nil
  ) -> SegmentedSelector {
    let options = [
      SegmentedOption(id: TransactionType.expense.rawValue, title: "支出"),
      SegmentedOption(id: TransactionType.income.rawValue, title: "收入"),
    ]

    let viewModel = SegmentedSelectorVM(
      options: options,
      selectedOptionId: selectedType.rawValue,
      onSelectionChanged: { optionId in
        if let type = TransactionType(rawValue: optionId) {
          onTypeChanged?(type)
        }
      }
    )

    return SegmentedSelector(viewModel: viewModel)
  }

  /// 创建卡片类型选择器
  static func cardType(
    isCredit: Bool,
    onTypeChanged: ((Bool) -> Void)? = nil
  ) -> SegmentedSelector {
    let options = [
      SegmentedOption(id: "debit", title: "储蓄卡片"),
      SegmentedOption(id: "credit", title: "信用卡片"),
    ]

    let viewModel = SegmentedSelectorVM(
      options: options,
      selectedOptionId: isCredit ? "credit" : "debit",
      onSelectionChanged: { optionId in
        onTypeChanged?(optionId == "credit")
      }
    )

    return SegmentedSelector(viewModel: viewModel)
  }
}

// MARK: - Preview

#if DEBUG
  struct SegmentedSelector_Previews: PreviewProvider {
    static var previews: some View {
      Group {
        // 交易类型选择器
        SegmentedSelector.transactionType(
          selectedType: .expense,
          onTypeChanged: { type in
            print("交易类型切换到: \(type)")
          }
        )
        .previewDisplayName("交易类型选择器")

        // 卡片类型选择器
        SegmentedSelector.cardType(
          isCredit: false,
          onTypeChanged: { isCredit in
            print("卡片类型切换到: \(isCredit ? "信用卡" : "储蓄卡")")
          }
        )
        .previewDisplayName("卡片类型选择器")
      }
      .padding()
      .background(Color.cLightBlue)
    }
  }
#endif
