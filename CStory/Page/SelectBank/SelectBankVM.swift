//
//  SelectBankVM.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/19.
//

import Foundation
import SwiftUI

/// 银行选择视图模型
///
/// 核心逻辑：
/// 1. 从JSON文件加载银行列表数据
/// 2. 加载热门银行数据
/// 3. 按字母分组银行列表
/// 4. 处理搜索过滤功能
/// 5. 管理银行图标异步加载
/// 6. 处理银行选择逻辑
final class SelectBankVM: ObservableObject {

  // MARK: - 输入参数

  /// 是否为信用卡
  let isCredit: Bool
  /// 主分类信息
  let mainCategory: CardCategoryResponse

  // MARK: - 数据源

  /// 所有银行列表
  @Published var banks: [CardSubCategoryResponse] = []
  /// 热门银行列表
  @Published var popularBanks: [CardSubCategoryResponse] = []
  /// 银行图标缓存
  @Published var bankImages: [String: Data] = [:]

  // MARK: - UI状态

  /// 搜索文本
  @Published var searchText: String = "" {
    didSet {
      // 搜索文本变化时不需要重新分组，filteredBanks会自动更新
    }
  }

  /// 按字母分组的银行数据
  @Published var groupedBanks: [String: [CardSubCategoryResponse]] = [:]
  /// 分组标题列表（按顺序）
  @Published var sectionTitles: [String] = []
  /// 数据是否已加载
  @Published var isDataLoaded: Bool = false

  /// 选中的银行（用于创建卡片）
  @Published var selectedSubCategory: CardSubCategoryResponse?
  /// 是否显示卡片创建详情
  @Published var showingCardDetail: Bool = false

  // MARK: - 计算属性

  /// 过滤后的银行列表（用于搜索）
  var filteredBanks: [CardSubCategoryResponse] {
    if searchText.isEmpty {
      return banks
    }
    return banks.filter { $0.displayName.contains(searchText) }
  }

  /// 默认银行图标
  private let defaultBankIcon = UIImage(named: "bank_default_icon")?.pngData()
  /// 触觉反馈管理器
  private let hapticManager = HapticFeedbackManager.shared

  /// A-Z 索引字母
  let indexLetters = [
    "热门", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R",
    "S", "T", "U", "V", "W", "X", "Y", "Z",
  ]

  // MARK: - 初始化

  init(isCredit: Bool, mainCategory: CardCategoryResponse) {
    self.isCredit = isCredit
    self.mainCategory = mainCategory

    // 开始加载数据
    loadData()
  }

  // MARK: - 公共方法

  /// 处理银行选择
  /// - Parameter bank: 选中的银行
  @MainActor
  func handleBankSelection(_ bank: CardSubCategoryResponse) {
    hapticManager.trigger(.impactLight)
    selectedSubCategory = bank
    withAnimation(.interactiveSpring(response: 0.3, dampingFraction: 0.8, blendDuration: 0.4)) {
      showingCardDetail = true
    }
  }

  /// 关闭卡片创建详情
  func dismissCardDetail() {
    withAnimation(.interactiveSpring(response: 0.3, dampingFraction: 0.8, blendDuration: 0.4)) {
      showingCardDetail = false
    }
    // 重置选中的银行
    selectedSubCategory = nil
  }

  /// 异步加载银行图标
  @MainActor
  func loadBankImage(for bank: CardSubCategoryResponse) async {
    guard bankImages[bank.displayName] == nil else { return }

    await Task.detached {
      if let image = UIImage(named: bank.displayName),
        let imageData = image.pngData()
      {
        await MainActor.run {
          self.bankImages[bank.displayName] = imageData
        }
      }
    }.value
  }

  /// 获取银行图标数据
  func getBankImageData(for bank: CardSubCategoryResponse) -> Data? {
    return bankImages[bank.displayName] ?? defaultBankIcon
  }

  // MARK: - 私有方法

  /// 加载数据的主方法
  private func loadData() {
    // 只有银行卡和信用卡才需要加载银行列表
    if mainCategory.name == "银行卡" || mainCategory.name == "信用卡" {
      Task {
        await MainActor.run {
          // 加载银行列表
          banks = CardCategoryJSONDecoder.decodeBankList()
          // 加载热门银行
          loadPopularBanks()
          // 按字母分组
          groupBanksByAlphabet()
          // 标记数据已加载
          isDataLoaded = true
        }
      }
    }
  }

  /// 加载热门银行列表
  private func loadPopularBanks() {
    guard let url = Bundle.main.url(forResource: "PopularBanks", withExtension: "json"),
      let data = try? Data(contentsOf: url),
      let popularBanksData = try? JSONDecoder().decode(PopularBanksResponse.self, from: data)
    else {
      return
    }

    popularBanks = banks.filter { bank in
      popularBanksData.popularBanks.contains { popularBank in
        bank.name == popularBank.name
      }
    }
  }

  /// 按字母分组银行列表
  private func groupBanksByAlphabet() {
    // 清空现有分组
    groupedBanks.removeAll()
    sectionTitles.removeAll()

    // 添加热门银行分组
    if !popularBanks.isEmpty {
      groupedBanks["热门"] = popularBanks
      sectionTitles.append("热门")
    }

    // 按首字母分组其他银行
    let alphabetGroups = Dictionary(grouping: banks) { bank in
      String(bank.name.prefix(1))
    }

    // 按字母顺序添加分组
    let sortedKeys = alphabetGroups.keys.sorted()
    for key in sortedKeys {
      if let banksInGroup = alphabetGroups[key], !banksInGroup.isEmpty {
        groupedBanks[key] = banksInGroup.sorted { $0.displayName < $1.displayName }
        sectionTitles.append(key)
      }
    }
  }
}
