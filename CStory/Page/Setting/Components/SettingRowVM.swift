//
//  SettingRowVM.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/25.
//

import Foundation
import SwiftUI

/// 设置行视图模型
///
/// 管理设置行组件的业务逻辑和数据处理，遵循MVVM架构模式。
/// 负责设置项数据的格式化、状态管理和交互逻辑。
///
/// 该类将设置项数据转换为视图层可直接使用的格式，
/// 包括图标显示、文本格式化、危险状态管理等功能。
///
/// ## 主要功能
/// - 设置项数据格式化和管理
/// - 危险操作状态控制
/// - 点击事件处理
/// - 视觉样式计算
///
/// ## 使用示例
/// ```swift
/// let viewModel = SettingRowVM(
///   icon: "category_icon",
///   title: "分类管理",
///   isDangerous: false
/// )
/// viewModel.onTap = { /* 处理点击事件 */ }
/// SettingRow(viewModel: viewModel)
/// ```
///
/// - Author: 咩咩
/// - Since: 2025.7.25
/// - Note: 该类支持动态数据更新，视图会自动响应属性变化
class SettingRowVM: ObservableObject {

  // MARK: - Published Properties

  /// 图标名称
  ///
  /// 设置项左侧显示的图标名称。
  /// 应该是应用资源包中存在的图片资源名称。
  @Published var icon: String

  /// 标题文本
  ///
  /// 设置项显示的主要文本内容。
  /// 使用系统字体的中等粗细显示。
  @Published var title: String

  /// 是否为危险操作
  ///
  /// 标识该设置项是否为危险操作（如删除数据）。
  /// 危险操作会显示红色文本以警示用户。
  @Published var isDangerous: Bool

  // MARK: - Interaction

  /// 点击事件回调
  ///
  /// 当设置行被点击时执行的闭包，通常用于导航或执行特定操作。
  var onTap: (() -> Void)?

  // MARK: - Initialization

  /// 初始化设置行视图模型
  ///
  /// - Parameters:
  ///   - icon: 图标名称
  ///   - title: 标题文本
  ///   - isDangerous: 是否为危险操作，默认为 false
  ///   - onTap: 点击事件回调
  init(
    icon: String,
    title: String,
    isDangerous: Bool = false,
    onTap: (() -> Void)? = nil
  ) {
    self.icon = icon
    self.title = title
    self.isDangerous = isDangerous
    self.onTap = onTap
  }

  // MARK: - 便利初始化方法

  /// 从SettingItem创建SettingRowVM
  ///
  /// - Parameters:
  ///   - item: 设置项数据模型
  ///   - onTap: 点击事件回调
  convenience init(
    from item: SettingItem,
    onTap: (() -> Void)? = nil
  ) {
    self.init(
      icon: item.icon,
      title: item.title,
      isDangerous: item.isDangerous,
      onTap: onTap
    )
  }

  // MARK: - Public Methods

  /// 更新设置项信息
  ///
  /// 批量更新设置项的基本信息。
  /// 用于动态修改设置项的显示内容。
  ///
  /// - Parameters:
  ///   - icon: 新的图标名称
  ///   - title: 新的标题文本
  ///   - isDangerous: 新的危险状态
  func updateInfo(icon: String? = nil, title: String? = nil, isDangerous: Bool? = nil) {
    if let icon = icon {
      self.icon = icon
    }
    if let title = title {
      self.title = title
    }
    if let isDangerous = isDangerous {
      self.isDangerous = isDangerous
    }
  }

  /// 设置危险状态
  ///
  /// 单独设置危险操作状态。
  /// 用于动态控制设置项的危险警示显示。
  ///
  /// - Parameter dangerous: 是否为危险操作
  func setDangerous(_ dangerous: Bool) {
    isDangerous = dangerous
  }

}
