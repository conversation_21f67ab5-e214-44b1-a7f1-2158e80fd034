//
//  CategoryFormVM.swift
//  CStory
//
//  Created by NZUE on 2025/12/19.
//

import AVFoundation
import ImageIO
import MobileCoreServices
import SwiftData
import SwiftUI

/// 类别表单视图模型
///
/// 负责处理类别创建和编辑的业务逻辑，遵循MVVM架构模式。
/// 该类从DataManagement获取数据，处理表单状态管理、验证和保存操作。
///
/// ## 主要职责
/// - 表单数据状态管理（名称、图标、模式等）
/// - 表单验证逻辑（名称验证、图标验证）
/// - 图片处理和压缩（HEIF格式转换）
/// - 类别的创建和编辑操作
/// - 与DataManagement集成的数据管理
///
/// ## 数据流向
/// ```
/// DataManagement → CategoryFormVM → CategoryFormView
/// ```
final class CategoryFormVM: ObservableObject {

  // MARK: - Dependencies

  /// 数据管理器，提供统一的数据访问接口
  private var dataManager: DataManagement?

  /// SwiftData 模型上下文，用于数据持久化
  private var modelContext: ModelContext?

  /// 表单模式（创建/编辑）
  private var mode: CategoryFormMode?

  // MARK: - Published Properties

  /// 类别名称
  @Published var name: String = ""

  /// 类别图标
  @Published var icon: IconType = .emoji("💰")

  /// Emoji 输入内容
  @Published var emojiInput: String = ""

  /// 选中的图标模式（Emoji/图片）
  @Published var selectedIconMode: IconMode = .emoji

  /// 选中的图片
  @Published var selectedImage: UIImage? = nil

  /// 是否显示图片选择器
  @Published var showImagePicker = false

  /// 名称是否为空（用于验证提示）
  @Published var isNameEmpty = false

  /// 是否显示保存动画
  @Published var showSaveAnimation = false

  /// 触发反馈（用于震动反馈）
  @Published var triggerFeedback = false

  /// 裁剪后的图片
  @Published var croppedImage: UIImage?

  /// 是否为初始加载状态
  @Published var isInitialLoad = true

  // MARK: - Private Properties

  /// 编辑模式下的类别ID
  private var editingCategoryId: String?

  /// 是否编辑主类别
  private var isEditingMainCategory: Bool = false

  // MARK: - Computed Properties

  /// 是否可以保存
  var canSave: Bool {
    // 名称不能为空
    guard !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
      return false
    }

    // 如果选择emoji模式，必须输入emoji
    if selectedIconMode == .emoji
        && emojiInput.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
      return false
    }

    // 如果选择图片模式，必须选择图片或者已经有图片数据
    if selectedIconMode == .image {
      let hasSelectedImage = selectedImage != nil
      let hasIconImageData = {
        if case .image(_) = icon {
          return true
        }
        return false
      }()

      if !hasSelectedImage && !hasIconImageData {
        return false
      }
    }

    return true
  }

  // MARK: - Enums

  /// 图标模式枚举
  enum IconMode: String, CaseIterable {
    case emoji = "Emoji"
    case image = "图片"
  }

  // MARK: - Initialization

  /// 默认初始化方法
  ///
  /// 创建未配置的ViewModel实例，需要后续调用configure方法进行配置
  init() {
    // 空初始化，等待配置
  }

  /// 配置ViewModel
  ///
  /// 在View的onAppear中调用，使用环境变量进行配置
  ///
  /// - Parameters:
  ///   - mode: 表单模式（创建或编辑）
  ///   - dataManager: 数据管理器
  ///   - modelContext: SwiftData模型上下文
  func configure(mode: CategoryFormMode, dataManager: DataManagement, modelContext: ModelContext) {
    self.mode = mode
    self.dataManager = dataManager
    self.modelContext = modelContext

    // 根据模式设置编辑相关属性
    switch mode {
    case .edit(let categoryId, let isMainCategory):
      self.editingCategoryId = categoryId
      self.isEditingMainCategory = isMainCategory
    case .create:
      self.editingCategoryId = nil
      self.isEditingMainCategory = true
    }

    // 加载初始数据
    loadInitialData()
  }

  /// 初始化类别表单视图模型（保持向后兼容）
  ///
  /// - Parameters:
  ///   - mode: 表单模式（创建或编辑）
  ///   - dataManager: 数据管理器
  ///   - modelContext: SwiftData模型上下文
  init(mode: CategoryFormMode, dataManager: DataManagement, modelContext: ModelContext) {
    self.mode = mode
    self.dataManager = dataManager
    self.modelContext = modelContext

    // 根据模式设置编辑相关属性
    switch mode {
    case .edit(let categoryId, let isMainCategory):
      self.editingCategoryId = categoryId
      self.isEditingMainCategory = isMainCategory
    case .create:
      self.editingCategoryId = nil
      self.isEditingMainCategory = true
    }

    // 加载初始数据
    loadInitialData()
  }

  // MARK: - Public Methods

  /// 处理选中图片变化
  func handleSelectedImageChange() {
    guard !isInitialLoad, let image = selectedImage else {
      return
    }

    // 使用HEIF压缩保存图片
    if let heifData = convertImageToHEIF(image: image, quality: 0.8) {
      icon = .image(heifData)
    } else {
      // HEIF转换失败时回退到PNG
      if let pngData = image.pngData() {
        icon = .image(pngData)
      }
    }
  }

  /// 处理裁剪图片变化
  func handleCroppedImageChange() {
    guard let image = croppedImage else { return }

    // 设置为初始加载状态，避免触发selectedImage的处理
    isInitialLoad = true
    selectedImage = image

    // 裁剪图片总是需要处理，因为是用户主动操作
    if let heifData = convertImageToHEIF(image: image, quality: 0.8) {
      icon = .image(heifData)
    } else {
      // HEIF转换失败时回退到PNG
      if let pngData = image.pngData() {
        icon = .image(pngData)
      }
    }

    // 处理完成后恢复正常状态
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
      self.isInitialLoad = false
    }
  }

  /// 处理emoji输入变化
  func handleEmojiInputChange() {
    if let firstEmoji = emojiInput.first {
      icon = .emoji(String(firstEmoji))
    }
  }

  /// 处理名称变化
  func handleNameChange() {
    isNameEmpty = name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
  }

  /// 完成初始加载
  func completeInitialLoad() {
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
      self.isInitialLoad = false
    }
  }

  /// 处理保存操作
  func handleSave() -> Bool {
    guard let modelContext = modelContext, let mode = mode else { return false }

    // 验证类别名称
    guard !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
      isNameEmpty = true
      return false
    }

    // 验证图标：如果选择了emoji模式，必须输入emoji
    if selectedIconMode == .emoji
        && emojiInput.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
      triggerFeedback.toggle()
      return false
    }

    // 验证图标：如果选择了图片模式，必须选择图片或者已经有图片数据
    if selectedIconMode == .image {
      let hasSelectedImage = selectedImage != nil
      let hasIconImageData = {
        if case .image(_) = icon {
          return true
        }
        return false
      }()

      if !hasSelectedImage && !hasIconImageData {
        triggerFeedback.toggle()
        return false
      }
    }

    // 执行保存操作
    switch mode {
    case .create(let isMainCategory, let mainCategoryId, let selectedType):
      handleCreate(isMainCategory: isMainCategory, mainCategoryId: mainCategoryId, selectedType: selectedType)
    case .edit:
      handleEdit()
    }

    // 显示保存动画
    withAnimation(.easeInOut(duration: 0.3)) {
      showSaveAnimation = true
    }

    // 保存数据上下文
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
      try? modelContext.save()
    }

    return true
  }

  // MARK: - Private Methods

  /// 加载初始数据
  private func loadInitialData() {
    guard let mode = mode else { return }
    if case .edit = mode {
      loadExistingCategoryData()
    }
  }

  /// 加载现有类别数据（编辑模式）
  private func loadExistingCategoryData() {
    guard let categoryId = editingCategoryId, let dataManager = dataManager else { return }

    if isEditingMainCategory {
      if let category = dataManager.mainCategories.first(where: { $0.id == categoryId }) {
        name = category.name
        icon = category.icon

        switch category.icon {
        case .emoji(let emoji):
          selectedIconMode = .emoji
          emojiInput = emoji
        case .image(let data):
          selectedIconMode = .image
          selectedImage = UIImage(data: data)
        }
      }
    } else {
      if let category = dataManager.subCategories.first(where: { $0.id == categoryId }) {
        name = category.name
        icon = category.icon

        switch category.icon {
        case .emoji(let emoji):
          selectedIconMode = .emoji
          emojiInput = emoji
        case .image(let data):
          selectedIconMode = .image
          selectedImage = UIImage(data: data)
        }
      }
    }
  }

  /// 处理创建操作
  private func handleCreate(isMainCategory: Bool, mainCategoryId: String?, selectedType: TransactionType) {
    guard let modelContext = modelContext else { return }

    if isMainCategory {
      let categoryId = generateMainCategoryId(selectedType: selectedType)
      let order = calculateMainCategoryOrder(selectedType: selectedType)

      let mainCategory = TransactionMainCategoryModel(
        id: categoryId,
        name: name,
        icon: icon,
        order: order,
        type: selectedType.rawValue
      )
      modelContext.insert(mainCategory)
    } else {
      guard let mainCategoryId = mainCategoryId else { return }
      let categoryId = generateSubCategoryId(mainCategoryId: mainCategoryId)
      let order = calculateSubCategoryOrder(mainCategoryId: mainCategoryId)

      let subCategory = TransactionSubCategoryModel(
        id: categoryId,
        name: name,
        icon: icon,
        order: order,
        mainId: mainCategoryId
      )
      modelContext.insert(subCategory)

      // 关联到主类别
      if let mainCategory = try? modelContext.fetch(
        FetchDescriptor<TransactionMainCategoryModel>(
          predicate: #Predicate<TransactionMainCategoryModel> { category in
            category.id == mainCategoryId
          }
        )
      ).first {
        if mainCategory.subCategories == nil {
          mainCategory.subCategories = []
        }
        mainCategory.subCategories?.append(subCategory)
      }
    }
  }

  /// 处理编辑操作
  private func handleEdit() {
    guard let categoryId = editingCategoryId, let dataManager = dataManager else { return }

    if isEditingMainCategory {
      if let category = dataManager.mainCategories.first(where: { $0.id == categoryId }) {
        category.name = name
        category.icon = icon
      }
    } else {
      if let category = dataManager.subCategories.first(where: { $0.id == categoryId }) {
        category.name = name
        category.icon = icon
      }
    }
  }

  // MARK: - Helper Methods

  /// HEIF格式转换函数
  private func convertImageToHEIF(image: UIImage, quality: CGFloat) -> Data? {
    let imageData = NSMutableData()

    guard
      let imageDestination = CGImageDestinationCreateWithData(
        imageData,
        AVFileType.heic as CFString,
        1,
        nil
      ), let originalCGImage = image.cgImage
    else {
      return nil
    }

    // 检查是否真正需要Alpha通道（检测是否有透明像素）
    let needsAlpha = imageHasTransparentPixels(cgImage: originalCGImage)

    var cgImageToUse = originalCGImage

    // 如果不需要Alpha通道，重新创建不带Alpha的CGImage
    if !needsAlpha {
      if let opaqueImage = createOpaqueImage(from: originalCGImage) {
        cgImageToUse = opaqueImage
      }
    }

    let options: [CFString: Any] = [
      kCGImageDestinationLossyCompressionQuality: quality
    ]

    CGImageDestinationAddImage(imageDestination, cgImageToUse, options as CFDictionary)

    guard CGImageDestinationFinalize(imageDestination) else {
      return nil
    }

    return imageData as Data
  }

  /// 检查图片是否有透明像素
  private func imageHasTransparentPixels(cgImage: CGImage) -> Bool {
    let alphaInfo = cgImage.alphaInfo

    if alphaInfo == .none || alphaInfo == .noneSkipFirst || alphaInfo == .noneSkipLast {
      return false
    }

    return hasActualTransparentPixels(cgImage: cgImage)
  }

  /// 通过采样检测图片是否真正包含透明像素
  private func hasActualTransparentPixels(cgImage: CGImage) -> Bool {
    let width = cgImage.width
    let height = cgImage.height

    let sampleStep = max(1, min(width, height) / 20)

    guard let dataProvider = cgImage.dataProvider,
          let data = dataProvider.data,
          let bytes = CFDataGetBytePtr(data)
    else {
      return true
    }

    let bytesPerPixel = cgImage.bitsPerPixel / 8
    let bytesPerRow = cgImage.bytesPerRow

    for y in stride(from: 0, to: height, by: sampleStep) {
      for x in stride(from: 0, to: width, by: sampleStep) {
        let pixelOffset = y * bytesPerRow + x * bytesPerPixel

        var alphaIndex: Int
        switch cgImage.alphaInfo {
        case .premultipliedLast, .last:
          alphaIndex = pixelOffset + bytesPerPixel - 1
        case .premultipliedFirst, .first:
          alphaIndex = pixelOffset
        default:
          continue
        }

        if alphaIndex < CFDataGetLength(data) {
          let alphaValue = bytes[alphaIndex]
          if alphaValue < 255 {
            return true
          }
        }
      }
    }

    return false
  }

  /// 创建不带Alpha通道的CGImage
  private func createOpaqueImage(from cgImage: CGImage) -> CGImage? {
    let width = cgImage.width
    let height = cgImage.height

    guard let colorSpace = cgImage.colorSpace else { return nil }

    guard
      let context = CGContext(
        data: nil,
        width: width,
        height: height,
        bitsPerComponent: 8,
        bytesPerRow: 0,
        space: colorSpace,
        bitmapInfo: CGImageAlphaInfo.noneSkipLast.rawValue
      )
    else { return nil }

    context.setFillColor(UIColor.white.cgColor)
    context.fill(CGRect(x: 0, y: 0, width: width, height: height))

    context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

    return context.makeImage()
  }

  /// 计算主类别的顺序
  private func calculateMainCategoryOrder(selectedType: TransactionType) -> Int {
    guard let modelContext = modelContext else { return 0 }

    let descriptor = FetchDescriptor<TransactionMainCategoryModel>(
      predicate: #Predicate<TransactionMainCategoryModel> { category in
        category.type == selectedType.rawValue
      },
      sortBy: [SortDescriptor(\TransactionMainCategoryModel.order, order: .reverse)]
    )

    if let lastCategory = try? modelContext.fetch(descriptor).first {
      return lastCategory.order + 1
    }
    return 0
  }

  /// 计算子类别的顺序
  private func calculateSubCategoryOrder(mainCategoryId: String) -> Int {
    guard let modelContext = modelContext else { return 0 }

    let descriptor = FetchDescriptor<TransactionSubCategoryModel>(
      predicate: #Predicate<TransactionSubCategoryModel> { category in
        category.mainId == mainCategoryId
      },
      sortBy: [SortDescriptor(\TransactionSubCategoryModel.order, order: .reverse)]
    )

    if let lastCategory = try? modelContext.fetch(descriptor).first {
      return lastCategory.order + 1
    }
    return 0
  }

  /// 生成主类别ID
  private func generateMainCategoryId(selectedType: TransactionType) -> String {
    guard let modelContext = modelContext else {
      let startNumber = selectedType == .expense ? 1001 : 2001
      let prefix = selectedType == .expense ? "1" : "2"
      return String(format: "%@%03d", prefix, startNumber)
    }

    let prefix = selectedType == .expense ? "1" : "2"
    let descriptor = FetchDescriptor<TransactionMainCategoryModel>(
      predicate: #Predicate<TransactionMainCategoryModel> { category in
        category.type == selectedType.rawValue
      },
      sortBy: [SortDescriptor(\TransactionMainCategoryModel.id, order: .reverse)]
    )

    if let lastCategory = try? modelContext.fetch(descriptor).first {
      let lastNumber = Int(lastCategory.id.dropFirst()) ?? 0
      let nextNumber = lastNumber + 1
      let maxNumber = selectedType == .expense ? 1999 : 2999
      let finalNumber = min(nextNumber, maxNumber)
      return String(format: "%@%03d", prefix, finalNumber)
    }

    let startNumber = selectedType == .expense ? 1001 : 2001
    return String(format: "%@%03d", prefix, startNumber)
  }

  /// 生成子类别ID
  private func generateSubCategoryId(mainCategoryId: String) -> String {
    guard let modelContext = modelContext else {
      let prefix = String(mainCategoryId.prefix(1))
      let startNumber = prefix == "1" ? 10001 : 20001
      return String(format: "%@%04d", prefix, startNumber)
    }

    let prefix = String(mainCategoryId.prefix(1))
    let descriptor = FetchDescriptor<TransactionSubCategoryModel>(
      predicate: #Predicate<TransactionSubCategoryModel> { category in
        category.mainId == mainCategoryId
      },
      sortBy: [SortDescriptor(\TransactionSubCategoryModel.id, order: .reverse)]
    )

    if let lastCategory = try? modelContext.fetch(descriptor).first {
      let lastNumber = Int(lastCategory.id.dropFirst()) ?? 0
      let nextNumber = lastNumber + 1
      let maxNumber = prefix == "1" ? 19999 : 29999
      let finalNumber = min(nextNumber, maxNumber)
      return String(format: "%@%04d", prefix, finalNumber)
    }

    let startNumber = prefix == "1" ? 10001 : 20001
    return String(format: "%@%04d", prefix, startNumber)
  }
}
