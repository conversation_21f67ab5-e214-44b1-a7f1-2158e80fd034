//
//  CardCategoryTestView.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/19.
//

import SwiftUI

/// 卡片分类测试视图
///
/// 参考正式项目CardCategoryView的UI结构，使用CardCategoryTestVM处理业务逻辑。
/// 完全复制原版的UI样式、动画效果和交互逻辑。
struct CardCategoryView: View {

  // MARK: - 属性

  @StateObject private var viewModel = CardCategoryVM()
  @Environment(\.dismiss) private var dismiss
  @Environment(\.dataManager) private var dataManager
  @EnvironmentObject private var pathManager: PathManagerHelper

  // MARK: - 主体视图

  var body: some View {
    VStack(spacing: 0) {
      // 顶部导航栏
      NavigationBarKit.backOnly(title: "创建卡片") {
        dismiss()
      }

      // 类型选择器
      SegmentedSelector.cardType(
        isCredit: viewModel.isCredit,
        onTypeChanged: { creditType in
          viewModel.updateCardType(creditType)
        }
      )

      // 分类列表
      ScrollView {
        categoryScrollView(categories: viewModel.currentCategories)
      }
    }
    .overlay {
      // 卡片创建overlay（暂时显示占位内容）
      if viewModel.showingCardDetail, let selectedCategory = viewModel.selectedCategory {
        cardCreationOverlay(category: selectedCategory)
      }
    }
    .navigationBarTitleDisplayMode(.inline)
    .navigationBarBackButtonHidden(true)
    .background(Color.cLightBlue)
  }

  // MARK: - 子视图构建器

  /// 类别滚动视图
  private func categoryScrollView(categories: [CardCategoryResponse]) -> some View {
    let columns = [
      GridItem(.flexible(), spacing: 12),
      GridItem(.flexible(), spacing: 12),
    ]

    return LazyVGrid(columns: columns, spacing: 12) {
      ForEach(categories, id: \.id) { category in
        cardCategoryButton(category: category)
      }
    }
    .padding(.top, 12)
    .padding(.horizontal, 16)
  }

  /// 类别按钮
  private func cardCategoryButton(category: CardCategoryResponse) -> some View {
    Button {
      viewModel.handleCategorySelection(category, pathManager: pathManager)
    } label: {
      HStack(spacing: 8) {
        Image(category.imageUrl)
          .resizable()
          .scaledToFit()
          .frame(width: 44, height: 44)

        Text(category.name)
          .font(.system(size: 15, weight: .medium))
          .foregroundColor(.cBlack)
        Spacer()
      }
      .frame(maxWidth: .infinity)
      .padding(12)
      .background(Color.cWhite)
      .cornerRadius(16)
      .overlay(
        RoundedRectangle(cornerRadius: 16)
          .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
      )
    }
    .buttonStyle(ScaleButtonStyle())
  }

  /// 按钮缩放动画样式
  private struct ScaleButtonStyle: SwiftUI.ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
      configuration.label
        .scaleEffect(configuration.isPressed ? 0.98 : 1)
        .animation(.easeInOut(duration: 0.35), value: configuration.isPressed)
    }
  }

  /// 卡片创建overlay（使用正式的CardDetailView）
  private func cardCreationOverlay(category: CardCategoryResponse) -> some View {
    CardDetailView(
      card: nil,
      cardNamespace: nil,
      showingCardDetail: $viewModel.showingCardDetail,
      pathManager: pathManager,
      transactions: dataManager.allTransactions,
      currencies: dataManager.currencies,
      animationMode: .createCard,
      isCreatingCard: true,
      isCredit: viewModel.isCredit,
      mainCategory: category,
      subCategory: nil,
      onCardCreated: { newCard in
        print("✅ 卡片创建成功: \(newCard.name)")
        // 创建完成后关闭视图
        viewModel.dismissCardDetail()
      },
      dataManager: dataManager
    )
  }
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct CardCategoryTestView_Previews: PreviewProvider {
    static var previews: some View {
      CardCategoryView()
    }
  }
#endif
