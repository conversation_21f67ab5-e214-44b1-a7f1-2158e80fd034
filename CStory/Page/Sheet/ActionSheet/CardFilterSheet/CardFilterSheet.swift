//
//  CardFilterSheet.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 卡片筛选弹窗
///
/// 提供卡片类型筛选功能，支持显示所有卡片、仅储蓄卡或仅信用卡。
/// 使用统一的选项卡片样式，带有选中状态指示。
/// 使用 MVVM 架构，通过 CardFilterSheetVM 管理业务逻辑。
struct CardFilterSheet: View {

  // MARK: - ViewModel
  
  /// 视图模型
  @StateObject private var viewModel: CardFilterSheetVM
  
  // MARK: - 初始化
  
  /// 初始化卡片筛选弹窗
  /// - Parameters:
  ///   - viewModel: 卡片筛选弹窗视图模型
  init(viewModel: CardFilterSheetVM) {
    self._viewModel = StateObject(wrappedValue: viewModel)
  }

  // MARK: - 主体

  var body: some View {
    VStack(spacing: 12) {
      // 标题栏
      SheetTitle(
        title: "筛选卡片",
        button: "xmark.circle.fill",
        rightButtonAction: viewModel.handleDismiss
      )

      // MARK: 筛选选项列表
      ForEach(viewModel.filterOptions, id: \.title) { option in
        HStack(spacing: 12) {
          // 选项图标
          Image(systemName: option.icon)
            .font(.system(size: 20))
            .foregroundColor(
              viewModel.isOptionSelected(option) ? .white : Color.cAccentBlue
            )
            .frame(width: 44, height: 44)
            .background(
              viewModel.isOptionSelected(option)
                ? Color.cAccentBlue : Color.cAccentBlue.opacity(0.1)
            )
            .cornerRadius(12)

          // 选项标题
          Text(option.title)
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(
              viewModel.isOptionSelected(option) ? Color.cAccentBlue : Color.cBlack
            )

          Spacer()
        }
        .padding(4)
        .background(Color.cWhite)
        .cornerRadius(16)
        .overlay(
          RoundedRectangle(cornerRadius: 16)
            .strokeBorder(
              viewModel.isOptionSelected(option)
                ? Color.cAccentBlue : Color.cAccentBlue.opacity(0.08),
              lineWidth: 1
            )
        )
        .padding(.horizontal, 16)
        .onTapGesture {
          viewModel.handleOptionTap(option)
        }
      }

      Spacer()
    }

  }
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct CardFilterSheet_Previews: PreviewProvider {
    static var previews: some View {
      CardFilterPreviewContainer()
    }
  }

  struct CardFilterPreviewContainer: View {
    @State private var showSheet = false
    @State private var selectedFilter: Bool? = nil

    var body: some View {
      VStack {
        Button("显示卡片筛选菜单") {
          showSheet = true
        }
        .padding()
        .background(Color.cAccentBlue)
        .foregroundColor(.white)
        .cornerRadius(12)

        if let filter = selectedFilter {
          Text("当前筛选: \(filter ? "信用卡" : "储蓄卡")")
            .padding()
        } else {
          Text("当前筛选: 所有卡片")
            .padding()
        }
      }
      .frame(maxWidth: .infinity, maxHeight: .infinity)
      .background(Color.cLightBlue)
      .floatingSheet(
        isPresented: $showSheet,
        config: SheetBase(
          maxDetent: .height(200),
          cornerRadius: 24,
          interactiveDimiss: true,
          hPadding: 8,
          bPadding: 4
        )
      ) {
        CardFilterSheet(
          viewModel: CardFilterSheetVM.standard(
            dataManager: DataManagement(),
            selectedFilter: $selectedFilter,
            dismiss: { showSheet = false },
            onFilterChanged: { filter in
              selectedFilter = filter
              print("筛选条件改变: \(filter?.description ?? "所有卡片")")
            }
          )
        )
      }
    }
  }
#endif
