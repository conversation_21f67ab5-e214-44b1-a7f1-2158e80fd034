//
//  CategoryActionSheet.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 交易分类操作弹窗
///
/// 提供交易分类相关的操作选项，包括编辑分类信息、排序类别、迁移账单到其他分类、删除分类等功能。
/// 使用统一的操作选项卡片样式，支持危险操作的视觉区分。
/// 使用 MVVM 架构，通过 CategoryActionSheetVM 管理业务逻辑。
struct CategoryActionSheet: View {

  // MARK: - ViewModel
  
  /// 视图模型
  @StateObject private var viewModel: CategoryActionSheetVM
  
  // MARK: - 初始化
  
  /// 初始化交易分类操作弹窗
  /// - Parameters:
  ///   - viewModel: 交易分类操作弹窗视图模型
  init(viewModel: CategoryActionSheetVM) {
    self._viewModel = StateObject(wrappedValue: viewModel)
  }

  // MARK: - 主体

  var body: some View {
    VStack(spacing: 12) {
      // 标题栏
      SheetTitle(
        title: "类别操作",
        button: "xmark.circle.fill",
        rightButtonAction: viewModel.handleDismiss
      )

      // MARK: 操作选项列表
      ForEach(Array(viewModel.actionOptions.enumerated()), id: \.offset) { index, option in
        HStack(spacing: 12) {
          // 操作图标
          Image(systemName: option.icon)
            .font(.system(size: 20))
            .foregroundColor(option.isDestructive ? .white : Color.cAccentBlue)
            .frame(width: 44, height: 44)
            .background(
              option.isDestructive ? Color.cAccentRed : Color.cAccentBlue.opacity(0.1)
            )
            .cornerRadius(12)

          // 操作标题
          Text(option.title)
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(option.isDestructive ? Color.cAccentRed : Color.cBlack)

          Spacer()
        }
        .padding(4)
        .background(Color.cWhite)
        .cornerRadius(16)
        .overlay(
          RoundedRectangle(cornerRadius: 16)
            .strokeBorder(
              option.isDestructive
                ? Color.cAccentRed.opacity(0.2) : Color.cAccentBlue.opacity(0.08),
              lineWidth: 1
            )
        )
        .padding(.horizontal, 16)
        .onTapGesture {
          option.action()
        }
      }

      Spacer()
    }

  }
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct CategoryActionSheet_Previews: PreviewProvider {
    static var previews: some View {
      CategoryActionPreviewContainer()
    }
  }

  struct CategoryActionPreviewContainer: View {
    @State private var showSheet = false

    var body: some View {
      VStack {
        Button("显示类别操作菜单") {
          showSheet = true
        }
        .padding()
        .background(Color.cAccentBlue)
        .foregroundColor(.white)
        .cornerRadius(12)
      }
      .frame(maxWidth: .infinity, maxHeight: .infinity)
      .background(Color.cLightBlue)
      .floatingSheet(
        isPresented: $showSheet,
        config: SheetBase(
          maxDetent: .height(300),
          cornerRadius: 24,
          interactiveDimiss: true,
          hPadding: 8,
          bPadding: 4
        )
      ) {
        CategoryActionSheet(
          viewModel: CategoryActionSheetVM.standard(
            dataManager: DataManagement(),
            onEdit: { print("编辑类别") },
            onSort: { print("排序类别") },
            onMigrate: { print("迁移账单") },
            onDelete: { print("删除类别") },
            dismiss: { showSheet = false }
          )
        )
      }
    }
  }
#endif
