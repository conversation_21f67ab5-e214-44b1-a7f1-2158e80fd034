//
//  TransactionRecordVM.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/18.
//

import Combine
import SwiftUI

/// 交易记录视图模型
///
/// 核心逻辑：
/// 1. 从DataManagement获取所有交易
/// 2. 根据时间周期（年/月/周）和具体时间筛选交易
/// 3. 排除创建卡片和调整卡片类型的交易
/// 4. 计算收入和支出统计
/// 5. 按日期分组生成UI数据
///
/// 时间控制：
/// - 默认显示当月数据
/// - 支持年/月/周切换
/// - 支持上一个/下一个导航
/// - 时间变化时自动重新处理数据
@MainActor
final class TransactionRecordVM: ObservableObject {

  // MARK: - 数据源

  /// 数据管理器
  private let dataManager: DataManagement

  /// 交易点击回调
  private let onTransactionTap: ((TransactionModel) -> Void)?

  // MARK: - UI数据

  /// 收入支出卡片的ViewModel
  ///
  /// 预先准备好的收入支出统计数据，包含收入、支出金额和货币符号。
  /// View层可以直接使用，无需在UI层组装数据。
  @Published var incomeExpenseCardVM: IncomeExpenseCardVM

  /// 时间控制器 - 管理时间选择逻辑
  @Published var timeControlVM: TimeControlVM

  /// 按日期分组的交易数据（包含完整的TransactionRowVM）
  @Published var transactionDayGroups: [TransactionDayGroupWithRowVM] = []

  // MARK: - 初始化

  init(dataManager: DataManagement, onTransactionTap: ((TransactionModel) -> Void)? = nil) {
    self.dataManager = dataManager
    self.onTransactionTap = onTransactionTap

    // 初始化收入支出卡片ViewModel
    let baseCurrencyCode = CurrencyService.shared.baseCurrencyCode
    let currencySymbol = dataManager.currencies.first { $0.code == baseCurrencyCode }?.symbol ?? "¥"
    self.incomeExpenseCardVM = IncomeExpenseCardVM(
      income: 0.0,
      expense: 0.0,
      currencySymbol: currencySymbol
    )

    // 初始化时间控制器 - 先不设置回调
    self.timeControlVM = TimeControlVM(selectedPeriod: .month, currentDate: Date()) { _, _ in }

    // 设置时间控制器的回调
    self.timeControlVM.onDateChange = { [weak self] date, period in
      self?.processData()
    }

    // 处理初始数据（当月）
    processData()
  }

  // MARK: - 公共方法

  /// 当前选择的日期（从时间控制器获取）
  var currentDate: Date {
    return timeControlVM.currentDate
  }

  /// 当前选择的时间周期（从时间控制器获取）
  var selectedPeriod: TransactionTimePeriod {
    return timeControlVM.selectedPeriod
  }

  // MARK: - 核心数据处理

  /// 处理数据的核心方法
  /// 1. 从DataManagement获取所有交易
  /// 2. 根据时间周期和日期筛选交易（排除创建和调整类型）
  /// 3. 计算收入和支出
  /// 4. 按日期分组生成UI数据
  /// 5. 更新日期文本
  private func processData() {
    // 1. 获取本位货币代码
    let baseCurrencyCode = CurrencyService.shared.baseCurrencyCode

    // 2. 获取时间范围
    let (startDate, endDate) = getDateRange()

    // 3. 筛选交易（排除创建和调整类型）- 使用 TransactionQueryService
    let transactionsInRange = dataManager.allTransactions.filter { transaction in
      transaction.transactionDate >= startDate && transaction.transactionDate < endDate
    }
    let filteredTransactions = TransactionQueryService.shared.filterTransactions(
      transactionsInRange,
      byTypes: [.income, .expense, .transfer, .refund]
    )

    // 4. 计算总收支
    var totalIncome: Double = 0
    var totalExpense: Double = 0

    for transaction in filteredTransactions {
      let amounts = CurrencyService.shared.getTransactionIncomeExpenseAmounts(
        transaction: transaction,
        targetCurrency: baseCurrencyCode
      )
      totalIncome += amounts.income
      totalExpense += amounts.expense
    }

    // 5. 按日期分组 - 使用 TransactionQueryService
    let groupedByDate = TransactionQueryService.shared.groupTransactionsByDate(filteredTransactions)

    // 6. 生成UI数据（按日期倒序：最新的日期在前面）
    transactionDayGroups = groupedByDate.map { date, transactions in
      // 计算当日收支
      var dayIncome: Double = 0
      var dayExpense: Double = 0

      for transaction in transactions {
        let amounts = CurrencyService.shared.getTransactionIncomeExpenseAmounts(
          transaction: transaction,
          targetCurrency: baseCurrencyCode
        )
        dayIncome += amounts.income
        dayExpense += amounts.expense
      }

      // 生成完整的TransactionRowVM数组（按时间倒序：最新的交易在前面）
      let transactionRowVMs =
        transactions
        .sorted { $0.transactionDate > $1.transactionDate }  // 先按实际日期时间倒序排列
        .map { transaction in
          let relatedCard =
            dataManager.findCard(by: transaction.fromCardId)
            ?? dataManager.findCard(by: transaction.toCardId)
          let categoryInfo = dataManager.getCategoryInfo(for: transaction.transactionCategoryId)

          // 使用TransactionRowVM的完整便利初始化，与HomeVM完全一致
          return TransactionRowVM(
            transaction: transaction,
            relatedCard: relatedCard,
            categoryInfo: categoryInfo,
            onTap: {
              // 震动反馈 - 统一使用.selection强度
              HapticFeedbackManager.shared.trigger(.selection)
              self.onTransactionTap?(transaction)
            }
          )
        }

      return (
        date: date,
        group: TransactionDayGroupWithRowVM(
          dateText: formatDateText(date),
          dayIncome: dayIncome,
          dayExpense: dayExpense,
          transactionRowVMs: transactionRowVMs
        )
      )
    }.sorted { first, second in
      // 按实际日期倒序排列，最新的日期在前面
      return first.date > second.date
    }.map { $0.group }  // 只保留TransactionDayGroupData

    // 7. 更新UI属性
    incomeExpenseCardVM.income = totalIncome
    incomeExpenseCardVM.expense = totalExpense
  }

  // MARK: - 辅助方法

  /// 获取时间范围（使用时间控制器的计算结果）
  private func getDateRange() -> (start: Date, end: Date) {
    return timeControlVM.dateRange
  }

  /// 格式化日期文本（用于分组显示）
  private func formatDateText(_ date: Date) -> String {
    return TransactionDisplayService.shared.formatTransactionTime(date, style: .dateOnly)
  }

}
