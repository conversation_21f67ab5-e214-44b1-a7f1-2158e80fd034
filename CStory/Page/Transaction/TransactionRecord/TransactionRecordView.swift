//
//  TransactionRecordTestView.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/18.
//

import SwiftUI

/// 交易记录测试视图
///
/// 这是一个纯粹的UI组件，它接收一个`TransactionRecordVM`对象并将其数据显示出来。
/// 所有的业务逻辑、数据格式化均由VM处理。
/// 参考现有TransactionRecordView的UI结构，保持一致的设计和样式。
struct TransactionRecordView: View {

  // MARK: - 属性

  // View只依赖于VM，不再关心原始数据
  @ObservedObject var viewModel: TransactionRecordVM
  @Environment(\.dismiss) private var dismiss
  @State private var hapticTrigger = false

  // MARK: - 主体视图

  var body: some View {
    VStack(spacing: 0) {
      // MARK: 导航栏
      NavigationBarKit.backOnly(title: "交易记录") {
        dismiss()
      }

      // MARK: 主要内容区域
      ScrollView {
        LazyVStack(spacing: 0) {
          // MARK: 时间选择组件 - 使用 ViewModel 模式
          TimeControl(viewModel: viewModel.timeControlVM)
            .padding(.horizontal, 16)

          // MARK: 收支汇总卡片 - 使用VM预先准备好的数据
          IncomeExpenseCard(viewModel: viewModel.incomeExpenseCardVM)
            .padding(.horizontal, 16)
            .padding(.top, 12)

          // MARK: 交易记录标题
          TitleKit(
            viewModel: TitleKitVM.titleOnly(title: "交易记录")
          )

          // MARK: 交易记录列表
          TransactionListContent(
            transactionDayGroups: viewModel.transactionDayGroups,
            currencySymbol: viewModel.incomeExpenseCardVM.currencySymbol
          )
        }
        .padding(.top, 12)
      }
    }
    .background(Color.cLightBlue)
    .navigationBarTitleDisplayMode(.inline)
    .navigationBarBackButtonHidden(true)
  }

  // MARK: - 子视图构建器
  // 已移除旧的私有方法，现在使用统一的TransactionListContent组件
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct TransactionRecordTestView_Previews: PreviewProvider {
    static var previews: some View {
      TransactionRecordView(
        viewModel: TransactionRecordVM(dataManager: createPreviewDataManager()))
    }

    /// 创建预览用的DataManagement
    static func createPreviewDataManager() -> DataManagement {
      // 创建示例货币
      let currencies = [
        CurrencyModel(
          name: "人民币",
          code: "CNY",
          symbol: "¥",
          rate: 1.0,
          isBaseCurrency: true,
          order: 0
        )
      ]

      // 创建示例卡片
      let cards = [
        CardModel(
          id: UUID(),
          order: 0,
          isCredit: false,
          isSelected: true,
          name: "招商银行储蓄卡",
          remark: "",
          currency: "CNY",
          symbol: "¥",
          balance: 12580.50,
          credit: 0,
          isStatistics: true,
          cover: "card1",
          bankName: "招商银行",
          cardNumber: "1234",
          isFixedDueDay: true,
          createdAt: Date(),
          updatedAt: Date()
        ),
        CardModel(
          id: UUID(),
          order: 1,
          isCredit: true,
          isSelected: true,
          name: "工商银行信用卡",
          remark: "",
          currency: "CNY",
          symbol: "¥",
          balance: -2580.30,
          credit: 10000.0,
          isStatistics: true,
          cover: "card2",
          bankName: "工商银行",
          cardNumber: "5678",
          isFixedDueDay: true,
          createdAt: Date(),
          updatedAt: Date()
        ),
      ]

      // 创建示例主分类
      let mainCategories = [
        TransactionMainCategoryModel(
          id: "expense_shopping",
          name: "购物",
          icon: .emoji("🛒"),
          order: 0,
          type: "expense"
        ),
        TransactionMainCategoryModel(
          id: "income_salary",
          name: "收入",
          icon: .emoji("💰"),
          order: 0,
          type: "income"
        ),
      ]

      // 创建示例子分类
      let subCategories = [
        TransactionSubCategoryModel(
          id: "shopping_daily",
          name: "日常用品",
          icon: .emoji("🧴"),
          order: 0,
          mainId: "expense_shopping"
        ),
        TransactionSubCategoryModel(
          id: "salary_main",
          name: "工资",
          icon: .emoji("💼"),
          order: 0,
          mainId: "income_salary"
        ),
      ]

      // 创建示例交易（跨越多个月份，便于测试时间筛选）
      let now = Date()
      let calendar = Calendar.current

      // 创建不同时间点
      let today = now
      let yesterday = calendar.date(byAdding: .day, value: -1, to: now) ?? now
      let threeDaysAgo = calendar.date(byAdding: .day, value: -3, to: now) ?? now
      let oneWeekAgo = calendar.date(byAdding: .day, value: -7, to: now) ?? now
      let twoWeeksAgo = calendar.date(byAdding: .day, value: -14, to: now) ?? now
      let lastMonth = calendar.date(byAdding: .month, value: -1, to: now) ?? now
      let twoMonthsAgo = calendar.date(byAdding: .month, value: -2, to: now) ?? now
      let threeMonthsAgo = calendar.date(byAdding: .month, value: -3, to: now) ?? now
      let lastYear = calendar.date(byAdding: .year, value: -1, to: now) ?? now

      let allTransactions = [
        // === 本月交易 ===
        TransactionModel(
          id: UUID(),
          transactionType: .expense,
          transactionCategoryId: "shopping_daily",
          fromCardId: cards[0].id,
          transactionAmount: 128.50,
          currency: "CNY",
          symbol: "¥",
          expenseToCardRate: 1.0,
          expenseToBaseRate: 1.0,
          incomeToCardRate: 1.0,
          incomeToBaseRate: 1.0,
          isStatistics: true,
          remark: "今天的购物",
          transactionDate: today,
          createdAt: today,
          updatedAt: today
        ),
        TransactionModel(
          id: UUID(),
          transactionType: .expense,
          transactionCategoryId: "shopping_daily",
          fromCardId: cards[1].id,
          transactionAmount: 89.90,
          currency: "CNY",
          symbol: "¥",
          expenseToCardRate: 1.0,
          expenseToBaseRate: 1.0,
          incomeToCardRate: 1.0,
          incomeToBaseRate: 1.0,
          isStatistics: true,
          remark: "昨天的支出",
          transactionDate: yesterday,
          createdAt: yesterday,
          updatedAt: yesterday
        ),
        TransactionModel(
          id: UUID(),
          transactionType: .income,
          transactionCategoryId: "salary_main",
          toCardId: cards[0].id,
          transactionAmount: 8500.00,
          currency: "CNY",
          symbol: "¥",
          expenseToCardRate: 1.0,
          expenseToBaseRate: 1.0,
          incomeToCardRate: 1.0,
          incomeToBaseRate: 1.0,
          isStatistics: true,
          remark: "本月工资",
          transactionDate: threeDaysAgo,
          createdAt: threeDaysAgo,
          updatedAt: threeDaysAgo
        ),
        TransactionModel(
          id: UUID(),
          transactionType: .expense,
          transactionCategoryId: "shopping_daily",
          fromCardId: cards[0].id,
          transactionAmount: 256.80,
          currency: "CNY",
          symbol: "¥",
          expenseToCardRate: 1.0,
          expenseToBaseRate: 1.0,
          incomeToCardRate: 1.0,
          incomeToBaseRate: 1.0,
          isStatistics: true,
          remark: "一周前的购物",
          transactionDate: oneWeekAgo,
          createdAt: oneWeekAgo,
          updatedAt: oneWeekAgo
        ),
        TransactionModel(
          id: UUID(),
          transactionType: .expense,
          transactionCategoryId: "shopping_daily",
          fromCardId: cards[1].id,
          transactionAmount: 445.20,
          currency: "CNY",
          symbol: "¥",
          expenseToCardRate: 1.0,
          expenseToBaseRate: 1.0,
          incomeToCardRate: 1.0,
          incomeToBaseRate: 1.0,
          isStatistics: true,
          remark: "两周前的支出",
          transactionDate: twoWeeksAgo,
          createdAt: twoWeeksAgo,
          updatedAt: twoWeeksAgo
        ),

        // === 上个月交易 ===
        TransactionModel(
          id: UUID(),
          transactionType: .income,
          transactionCategoryId: "salary_main",
          toCardId: cards[0].id,
          transactionAmount: 7800.00,
          currency: "CNY",
          symbol: "¥",
          expenseToCardRate: 1.0,
          expenseToBaseRate: 1.0,
          incomeToCardRate: 1.0,
          incomeToBaseRate: 1.0,
          isStatistics: true,
          remark: "上月工资",
          transactionDate: lastMonth,
          createdAt: lastMonth,
          updatedAt: lastMonth
        ),
        TransactionModel(
          id: UUID(),
          transactionType: .expense,
          transactionCategoryId: "shopping_daily",
          fromCardId: cards[1].id,
          transactionAmount: 1299.00,
          currency: "CNY",
          symbol: "¥",
          expenseToCardRate: 1.0,
          expenseToBaseRate: 1.0,
          incomeToCardRate: 1.0,
          incomeToBaseRate: 1.0,
          isStatistics: true,
          remark: "上月大额购物",
          transactionDate: lastMonth,
          createdAt: lastMonth,
          updatedAt: lastMonth
        ),

        // === 两个月前交易 ===
        TransactionModel(
          id: UUID(),
          transactionType: .income,
          transactionCategoryId: "salary_main",
          toCardId: cards[0].id,
          transactionAmount: 7500.00,
          currency: "CNY",
          symbol: "¥",
          expenseToCardRate: 1.0,
          expenseToBaseRate: 1.0,
          incomeToCardRate: 1.0,
          incomeToBaseRate: 1.0,
          isStatistics: true,
          remark: "两月前工资",
          transactionDate: twoMonthsAgo,
          createdAt: twoMonthsAgo,
          updatedAt: twoMonthsAgo
        ),
        TransactionModel(
          id: UUID(),
          transactionType: .expense,
          transactionCategoryId: "shopping_daily",
          fromCardId: cards[0].id,
          transactionAmount: 688.50,
          currency: "CNY",
          symbol: "¥",
          expenseToCardRate: 1.0,
          expenseToBaseRate: 1.0,
          incomeToCardRate: 1.0,
          incomeToBaseRate: 1.0,
          isStatistics: true,
          remark: "两月前购物",
          transactionDate: twoMonthsAgo,
          createdAt: twoMonthsAgo,
          updatedAt: twoMonthsAgo
        ),

        // === 三个月前交易 ===
        TransactionModel(
          id: UUID(),
          transactionType: .income,
          transactionCategoryId: "salary_main",
          toCardId: cards[1].id,
          transactionAmount: 7200.00,
          currency: "CNY",
          symbol: "¥",
          expenseToCardRate: 1.0,
          expenseToBaseRate: 1.0,
          incomeToCardRate: 1.0,
          incomeToBaseRate: 1.0,
          isStatistics: true,
          remark: "三月前工资",
          transactionDate: threeMonthsAgo,
          createdAt: threeMonthsAgo,
          updatedAt: threeMonthsAgo
        ),

        // === 去年交易 ===
        TransactionModel(
          id: UUID(),
          transactionType: .income,
          transactionCategoryId: "salary_main",
          toCardId: cards[0].id,
          transactionAmount: 6800.00,
          currency: "CNY",
          symbol: "¥",
          expenseToCardRate: 1.0,
          expenseToBaseRate: 1.0,
          incomeToCardRate: 1.0,
          incomeToBaseRate: 1.0,
          isStatistics: true,
          remark: "去年同期工资",
          transactionDate: lastYear,
          createdAt: lastYear,
          updatedAt: lastYear
        ),
      ]

      return DataManagement(
        cards: cards,
        mainCategories: mainCategories,
        subCategories: subCategories,
        currencies: currencies,
        recentTransactions: allTransactions,
        allTransactions: allTransactions,
        chatMessages: []
      )
    }
  }
#endif
