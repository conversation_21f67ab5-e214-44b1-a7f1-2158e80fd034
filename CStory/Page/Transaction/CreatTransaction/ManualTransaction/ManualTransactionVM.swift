//
//  ManualTransactionVM.swift
//  CStory
//
//  Created by NZUE on 2025/7/21.
//

import Foundation
import SwiftData
import SwiftUI

/// 手动交易视图模型
///
/// 专门负责手动记账流程的业务逻辑处理和数据管理，遵循新架构的MVVM模式。
/// 该类从DataManagement获取原始数据，进行业务逻辑处理后，
/// 为ManualTransactionView提供格式化的、可直接使用的数据。
///
/// ## 主要职责
/// - 手动交易类型选择和切换管理
/// - 金额输入和货币转换处理
/// - 卡片关联和选择逻辑
/// - 分类选择和验证
/// - 交易保存和数据持久化
/// - 手动交易特有的UI状态管理
///
/// ## 数据流向
/// ```
/// DataManagement → ManualTransactionVM → ManualTransactionView
/// ```
///
/// - Important: 使用DataManagement替代AppDataManager，确保数据一致性
/// - Note: 所有UI相关的数据处理都在ViewModel层完成
/// - Author: NZUE
/// - Version: 1.0 (New架构)
/// - Since: 2025.7.21
@MainActor
final class ManualTransactionVM: ObservableObject {

  // MARK: - Dependencies

  /// 数据管理器，提供原始数据源
  ///
  /// 通过DataManagement获取卡片、交易、分类等基础数据，
  /// 作为所有业务逻辑计算的数据来源。
  private var dataManager: DataManagement?

  // MARK: - UserDefaults Keys
  static let lastSelectedExpenseCardKey = "lastSelectedExpenseCard"
  static let lastSelectedIncomeCardKey = "lastSelectedIncomeCard"

  // MARK: - Published Properties

  // 基础设置
  @Published var baseCurrencyCode: String =
    UserDefaults.standard.string(forKey: "baseCurrencyCode") ?? "CNY"

  // 分类选择
  @Published var selectedExpenseCategoryId: String?
  @Published var selectedExpenseSubCategoryId: String?
  @Published var selectedIncomeCategoryId: String?
  @Published var selectedIncomeSubCategoryId: String?

  // 交易和控制状态
  @Published var transactionType: TransactionType = .expense
  @Published var controlBarState: ControlBarState = .numericKeypad

  // 货币代码和汇率
  @Published var expenseCurrencyCode: String = ""
  @Published var incomeCurrencyCode: String = ""
  @Published var transferCurrencyCode: String = ""
  @Published var convertCurrencyCode: String = ""
  @Published var conversionRate: String = "1.0"

  @Published var expenseToCardRate: Double = 1.0
  @Published var expenseToBaseRate: Double = 1.0
  @Published var incomeToCardRate: Double = 1.0
  @Published var incomeToBaseRate: Double = 1.0

  // 交易输入
  @Published var transactionAmount: String = "0"
  @Published var discountAmount: String = "0"
  @Published var transactionExpression: String = "0"
  @Published var discountExpression: String = "0"
  @Published var selectedDate: Date = .init()
  @Published var showDiscount: Bool = false

  // 选中的卡片
  @Published var selectedExpenseCardId: UUID?
  @Published var selectedIncomeCardId: UUID?
  @Published var selectedTransferSourceCardId: UUID?
  @Published var selectedTransferDestinationCardId: UUID?

  // 转账控制标志
  @Published var isTransferOutgoing: Bool = true
  @Published var isTransferCardSelected: Bool = false

  // 货币表单
  @Published var isCurrencySheetVisible: Bool = false

  // MARK: - Private Properties

  private var cards: [CardModel]?
  private var currencies: [CurrencyModel]?

  // MARK: - Initialization

  init() {
    setupInitialState()
  }

  // MARK: - Public Methods

  /// 配置ViewModel的依赖
  ///
  /// 注入数据管理器，为ViewModel提供数据源。
  /// 这个方法应该在ViewModel创建后立即调用。
  ///
  /// - Parameter dataManager: 数据管理器，提供基础数据源
  ///
  /// - Important: 必须在使用ViewModel的其他方法之前调用此方法
  func configure(with dataManager: DataManagement) {
    self.dataManager = dataManager
    self.cards = dataManager.cards
    self.currencies = dataManager.currencies
    loadDefaultCards()
  }

  /// 设置数据源（兼容性方法，逐步迁移）
  func setDataSources(cards: [CardModel], currencies: [CurrencyModel]) {
    self.cards = cards
    self.currencies = currencies
    loadDefaultCards()
  }

  /// 分类选择回调
  func onExpenseCategorySelected(_ mainId: String?, _ subId: String?) {
    selectedExpenseCategoryId = mainId
    selectedExpenseSubCategoryId = subId
  }

  func onIncomeCategorySelected(_ mainId: String?, _ subId: String?) {
    selectedIncomeCategoryId = mainId
    selectedIncomeSubCategoryId = subId
  }

  // MARK: - Computed Properties

  /// 当前选中的卡片
  var selectedCard: CardModel? {
    guard let cards = self.cards else { return nil }
    switch transactionType {
    case .expense: return cards.first { $0.id == selectedExpenseCardId }
    case .income: return cards.first { $0.id == selectedIncomeCardId }
    case .transfer:
      return cards.first {
        $0.id
          == (isTransferOutgoing ? selectedTransferSourceCardId : selectedTransferDestinationCardId)
      }
    default:
      return nil
    }
  }

  /// 当前货币代码
  var currentCurrencyCode: String {
    switch transactionType {
    case .expense: return expenseCurrencyCode
    case .income: return incomeCurrencyCode
    case .transfer: return transferCurrencyCode
    case .refund, .createCard, .adjustCard: return baseCurrencyCode
    }
  }

  /// 选择标题
  var selectionTitle: String {
    switch transactionType {
    case .transfer:
      return isTransferOutgoing ? "选择转出卡片" : "选择转入卡片"
    case .expense:
      return "选择支出卡片"
    case .income:
      return "选择收入卡片"
    default:
      return "选择卡片"
    }
  }

  // MARK: - UI State Computed Properties

  /// 卡片选择按钮文本
  var cardSelectionButtonText: String {
    controlBarState == .selectCard ? "返回" : selectedCard?.name ?? "选择卡片"
  }

  /// 卡片选择按钮背景色
  var cardSelectionButtonBackgroundColor: Color {
    controlBarState == .selectCard ? Color.cAccentBlue.opacity(0.1) : Color.cWhite
  }

  /// 卡片选择按钮边框色
  var cardSelectionButtonBorderColor: Color {
    controlBarState == .selectCard ? Color.cAccentBlue : Color.cAccentBlue.opacity(0.08)
  }

  /// 时间选择按钮背景色
  var timeSelectionButtonBackgroundColor: Color {
    controlBarState == .selectTime ? Color.cAccentBlue.opacity(0.1) : Color.cWhite
  }

  /// 时间选择按钮边框色
  var timeSelectionButtonBorderColor: Color {
    controlBarState == .selectTime ? Color.cAccentBlue : Color.cAccentBlue.opacity(0.08)
  }

  /// 格式化的时间显示
  var formattedTime: String {
    DateFormattingHelper.shared.format(date: selectedDate, as: .hourMinute)
  }

  /// 格式化的日期显示
  var formattedDate: String {
    DateFormattingHelper.shared.format(date: selectedDate, as: .chineseYearMonthDay)
  }

  /// 交易类型按钮前景色
  func transactionTypeButtonForegroundColor(for type: TransactionType) -> Color {
    transactionType == type ? Color.cWhite : Color.cBlack.opacity(0.6)
  }

  // MARK: - Card Selection Methods

  /// 检查资产是否被选中
  func isCardSelected(_ card: CardModel) -> Bool {
    switch transactionType {
    case .expense: return selectedExpenseCardId == card.id
    case .income: return selectedIncomeCardId == card.id
    case .transfer:
      return isTransferOutgoing
        ? selectedTransferSourceCardId == card.id : selectedTransferDestinationCardId == card.id
    case .refund, .createCard, .adjustCard:
      return false
    }
  }

  /// 选择资产
  func selectCard(_ card: CardModel) {
    switch transactionType {
    case .expense:
      updateExpenseCard(card)
    case .income:
      updateIncomeCard(card)
    case .transfer:
      updateTransferCard(card)
    case .refund, .createCard, .adjustCard:
      break
    }
    self.controlBarState = .numericKeypad

    logCardSelection(card)
    saveLastSelectedCard(card)
  }

  /// 清除选择
  func clearSelection() {
    switch transactionType {
    case .expense:
      selectedExpenseCardId = nil
      print("清除支出卡片，当前支出货币代码: \(expenseCurrencyCode)")
    case .income:
      selectedIncomeCardId = nil
      print("清除收入卡片，当前收入货币代码: \(incomeCurrencyCode)")
    case .transfer:
      if isTransferOutgoing {
        selectedTransferSourceCardId = nil
        if let destCard = cards?.first(where: { $0.id == selectedTransferDestinationCardId }) {
          transferCurrencyCode = destCard.currency
        } else {
          transferCurrencyCode = baseCurrencyCode
        }
      } else {
        selectedTransferDestinationCardId = nil
        if let sourceCard = cards?.first(where: { $0.id == selectedTransferSourceCardId }) {
          transferCurrencyCode = sourceCard.currency
        } else {
          transferCurrencyCode = baseCurrencyCode
        }
      }
      conversionRate =
        transferCurrencyCode == baseCurrencyCode
        ? "1.0" : String(getCurrencyRate(from: transferCurrencyCode, to: baseCurrencyCode) ?? 1.0)
    case .refund, .createCard, .adjustCard:
      break
    }
    controlBarState = .numericKeypad
  }

  // MARK: - UI Interaction Methods

  /// 处理交易类型按钮点击
  func handleTransactionTypeButtonTap(_ type: TransactionType) {
    transactionType = type
  }

  /// 处理卡片选择按钮点击
  func handleCardSelectionButtonTap() {
    controlBarState = controlBarState != .selectCard ? .selectCard : .numericKeypad
  }

  /// 处理时间选择按钮点击
  func handleTimeSelectionButtonTap() {
    controlBarState = controlBarState == .selectTime ? .numericKeypad : .selectTime
  }

  /// 处理货币按钮点击
  func handleCurrencyButtonTap() {
    transactionAmount = transactionExpression
    isCurrencySheetVisible = true
  }

  /// 处理优惠按钮点击
  func handleDiscountButtonTap() {
    withAnimation(.easeInOut(duration: 0.3)) {
      showDiscount.toggle()
    }
  }

  /// 处理确定按钮点击（卡片选择和时间选择）
  func handleConfirmButtonTap() {
    controlBarState = .numericKeypad
  }

  /// 处理回到今日按钮点击
  func handleBackToTodayButtonTap() {
    selectedDate = Date()
  }

  /// 处理交易金额变化
  func handleTransactionAmountChange() {
    if transactionExpression != transactionAmount {
      transactionExpression = transactionAmount.isEmpty ? "0" : transactionAmount
    }
  }

  /// 处理交易表达式变化
  func handleTransactionExpressionChange() {
    transactionAmount = transactionExpression
  }

  /// 处理优惠表达式变化
  func handleDiscountExpressionChange() {
    discountAmount = discountExpression
  }

  /// 处理交易类型变化时的优惠状态
  func handleTransactionTypeChangeForDiscount() {
    if transactionType != .expense {
      showDiscount = false
    }
  }
}

// MARK: - Private Methods

extension ManualTransactionVM {

  /// 初始化状态设置
  private func setupInitialState() {
    baseCurrencyCode = UserDefaults.standard.string(forKey: "baseCurrencyCode") ?? "CNY"
    expenseCurrencyCode = baseCurrencyCode
    incomeCurrencyCode = baseCurrencyCode
    transferCurrencyCode = baseCurrencyCode
    convertCurrencyCode = baseCurrencyCode
  }

  /// 加载默认资产
  private func loadDefaultCards() {
    guard let cards = self.cards else { return }

    // 加载上次选择的支出资产
    if let lastExpenseIdString = UserDefaults.standard.string(
      forKey: Self.lastSelectedExpenseCardKey),
      let lastExpenseId = UUID(uuidString: lastExpenseIdString),
      cards.contains(where: { $0.id == lastExpenseId && $0.isStatistics && $0.isSelected })
    {
      selectedExpenseCardId = lastExpenseId
      if let card = cards.first(where: { $0.id == lastExpenseId }) {
        expenseCurrencyCode = card.currency
        print("已加载上次选择的支出卡片: \(card.name)")
      }
    } else if let firstExpenseCard = cards.first(where: { $0.isStatistics && $0.isSelected }) {
      selectedExpenseCardId = firstExpenseCard.id
      expenseCurrencyCode = firstExpenseCard.currency
      print("已选择第一张可用的支出卡片: \(firstExpenseCard.name)")
    }

    // 加载上次选择的收入资产
    if let lastIncomeIdString = UserDefaults.standard.string(
      forKey: Self.lastSelectedIncomeCardKey),
      let lastIncomeId = UUID(uuidString: lastIncomeIdString),
      cards.contains(where: { $0.id == lastIncomeId && $0.isStatistics && $0.isSelected })
    {
      selectedIncomeCardId = lastIncomeId
      if let card = cards.first(where: { $0.id == lastIncomeId }) {
        incomeCurrencyCode = card.currency
        print("已加载上次选择的收入卡片: \(card.name)")
      }
    } else if let firstIncomeCard = cards.first(where: { $0.isStatistics && $0.isSelected }) {
      selectedIncomeCardId = firstIncomeCard.id
      incomeCurrencyCode = firstIncomeCard.currency
      print("已选择第一张可用的收入卡片: \(firstIncomeCard.name)")
    }
  }

  /// 保存上次选择的资产
  private func saveLastSelectedCard(_ card: CardModel) {
    switch transactionType {
    case .expense:
      UserDefaults.standard.set(card.id.uuidString, forKey: Self.lastSelectedExpenseCardKey)
      print("已保存支出卡片选择: \(card.name)")
    case .income:
      UserDefaults.standard.set(card.id.uuidString, forKey: Self.lastSelectedIncomeCardKey)
      print("已保存收入卡片选择: \(card.name)")
    case .transfer:
      break
    case .refund, .createCard, .adjustCard:
      break
    }
  }

  /// 记录资产选择日志
  private func logCardSelection(_ card: CardModel) {
    let selectionType =
      transactionType == .expense
      ? "支出卡片" : transactionType == .income ? "收入卡片" : isTransferOutgoing ? "转出卡片" : "转入卡片"
    let cardType = card.isCredit ? "信用卡" : "储蓄卡"
    print("已选择\(selectionType): \(card.name), 类型: \(cardType), 货币代码: \(card.currency)")
  }

  /// 更新支出资产
  private func updateExpenseCard(_ card: CardModel) {
    selectedExpenseCardId = card.id
    expenseCurrencyCode = card.currency
    expenseToBaseRate =
      (card.currency == baseCurrencyCode)
      ? 1.0 : (getCurrencyRate(from: card.currency, to: baseCurrencyCode) ?? 1.0)
    conversionRate = String(expenseToBaseRate)
  }

  /// 更新收入资产
  private func updateIncomeCard(_ card: CardModel) {
    selectedIncomeCardId = card.id
    incomeCurrencyCode = card.currency
    incomeToBaseRate =
      (card.currency == baseCurrencyCode)
      ? 1.0 : (getCurrencyRate(from: card.currency, to: baseCurrencyCode) ?? 1.0)
    conversionRate = String(incomeToBaseRate)
  }

  /// 更新转账资产
  private func updateTransferCard(_ card: CardModel) {
    if isTransferOutgoing {
      selectedTransferSourceCardId = card.id
      if selectedTransferDestinationCardId == nil {
        transferCurrencyCode = card.currency
      }
    } else {
      selectedTransferDestinationCardId = card.id
      if selectedTransferSourceCardId == nil {
        transferCurrencyCode = card.currency
      }
    }
    updateConversionRate()
  }

  /// 更新转换汇率
  private func updateConversionRate() {
    conversionRate =
      (transferCurrencyCode == baseCurrencyCode)
      ? "1.0" : String(getCurrencyRate(from: transferCurrencyCode, to: baseCurrencyCode) ?? 1.0)
  }

  /// 获取货币汇率
  private func getCurrencyRate(from sourceCurrency: String, to targetCurrency: String)
    -> Double?
  {
    guard let currencies = self.currencies,
      let source = currencies.first(where: { $0.code == sourceCurrency }),
      let target = currencies.first(where: { $0.code == targetCurrency })
    else {
      return nil
    }
    return source.rate / target.rate
  }

  /// 获取货币符号
  private func getCurrencySymbol() -> String {
    currencies?.first(where: { $0.code == currentCurrencyCode })?.symbol ?? "¥"
  }

  /// 创建交易记录
  func createTransaction(amount: Double, discount: Double) -> TransactionModel {
    switch transactionType {
    case .expense:
      calculateExpenseRates()
      return TransactionModel(
        id: UUID(),
        transactionType: .expense,
        transactionCategoryId: selectedExpenseSubCategoryId ?? selectedExpenseCategoryId,
        fromCardId: selectedExpenseCardId,
        toCardId: nil,
        discountAmount: discount > 0 ? discount : nil,
        transactionAmount: amount,
        currency: expenseCurrencyCode,
        symbol: getCurrencySymbol(),
        expenseToCardRate: expenseToCardRate,
        expenseToBaseRate: expenseToBaseRate,
        incomeToCardRate: 1.0,
        incomeToBaseRate: 1.0,
        isStatistics: true,
        remark: "",
        transactionDate: selectedDate,
        createdAt: Date(),
        updatedAt: Date()
      )
    case .income:
      calculateIncomeRates()
      return TransactionModel(
        id: UUID(),
        transactionType: .income,
        transactionCategoryId: selectedIncomeSubCategoryId ?? selectedIncomeCategoryId,
        fromCardId: nil,
        toCardId: selectedIncomeCardId,
        discountAmount: discount > 0 ? discount : nil,
        transactionAmount: amount,
        currency: incomeCurrencyCode,
        symbol: getCurrencySymbol(),
        expenseToCardRate: 1.0,
        expenseToBaseRate: 1.0,
        incomeToCardRate: incomeToCardRate,
        incomeToBaseRate: incomeToBaseRate,
        isStatistics: true,
        remark: "",
        transactionDate: selectedDate,
        createdAt: Date(),
        updatedAt: Date()
      )
    case .transfer:
      calculateTransferRates()
      return TransactionModel(
        id: UUID(),
        transactionType: .transfer,
        transactionCategoryId: "SYS_TRANSFER",
        fromCardId: selectedTransferSourceCardId,
        toCardId: selectedTransferDestinationCardId,
        transactionAmount: amount,
        currency: transferCurrencyCode,
        symbol: getCurrencySymbol(),
        expenseToCardRate: expenseToCardRate,
        expenseToBaseRate: expenseToBaseRate,
        incomeToCardRate: incomeToCardRate,
        incomeToBaseRate: incomeToBaseRate,
        isStatistics: true,
        remark: "",
        transactionDate: selectedDate,
        createdAt: Date(),
        updatedAt: Date()
      )
    default:
      // 为其他系统级交易类型设置对应的系统分类ID
      let systemCategoryId: String?
      switch transactionType {
      case .refund:
        systemCategoryId = "SYS_REFUND"
      case .createCard:
        systemCategoryId = "SYS_CREATE_CARD"
      case .adjustCard:
        systemCategoryId = "SYS_ADJUST_CARD"
      default:
        systemCategoryId = nil
      }

      return TransactionModel(
        id: UUID(),
        transactionType: transactionType,
        transactionCategoryId: systemCategoryId,
        transactionAmount: amount,
        currency: currentCurrencyCode,
        symbol: getCurrencySymbol(),
        expenseToCardRate: 1.0,
        expenseToBaseRate: 1.0,
        incomeToCardRate: 1.0,
        incomeToBaseRate: 1.0,
        isStatistics: true,
        remark: "",
        transactionDate: selectedDate,
        createdAt: Date(),
        updatedAt: Date()
      )
    }
  }

  /// 计算支出汇率
  private func calculateExpenseRates() {
    guard let cards = self.cards else { return }
    let userRate = Double(conversionRate) ?? 1.0
    if let cardCurrency = cards.first(where: { $0.id == selectedExpenseCardId })?.currency {
      if convertCurrencyCode == cardCurrency {
        expenseToCardRate = userRate
        expenseToBaseRate =
          expenseCurrencyCode == baseCurrencyCode
          ? 1.0
          : cardCurrency == baseCurrencyCode
            ? expenseToCardRate
            : userRate * (getCurrencyRate(from: cardCurrency, to: baseCurrencyCode) ?? 1.0)
      } else if convertCurrencyCode == baseCurrencyCode {
        expenseToBaseRate = userRate
        expenseToCardRate =
          expenseCurrencyCode == cardCurrency
          ? 1.0
          : cardCurrency == baseCurrencyCode
            ? expenseToBaseRate
            : userRate * (getCurrencyRate(from: baseCurrencyCode, to: cardCurrency) ?? 1.0)
      } else {
        expenseToCardRate =
          userRate * (getCurrencyRate(from: convertCurrencyCode, to: cardCurrency) ?? 1.0)
        expenseToBaseRate =
          userRate * (getCurrencyRate(from: convertCurrencyCode, to: baseCurrencyCode) ?? 1.0)
      }
    } else {
      expenseToBaseRate =
        convertCurrencyCode == baseCurrencyCode
        ? userRate
        : expenseCurrencyCode == baseCurrencyCode
          ? 1.0
          : userRate * (getCurrencyRate(from: convertCurrencyCode, to: baseCurrencyCode) ?? 1.0)
      expenseToCardRate = 1.0
    }
  }

  /// 计算收入汇率
  private func calculateIncomeRates() {
    guard let cards = self.cards else { return }
    let userRate = Double(conversionRate) ?? 1.0
    if let cardCurrency = cards.first(where: { $0.id == selectedIncomeCardId })?.currency {
      if convertCurrencyCode == cardCurrency {
        incomeToCardRate = userRate
        incomeToBaseRate =
          incomeCurrencyCode == baseCurrencyCode
          ? 1.0
          : cardCurrency == baseCurrencyCode
            ? incomeToCardRate
            : userRate * (getCurrencyRate(from: cardCurrency, to: baseCurrencyCode) ?? 1.0)
      } else if convertCurrencyCode == baseCurrencyCode {
        incomeToBaseRate = userRate
        incomeToCardRate =
          incomeCurrencyCode == cardCurrency
          ? 1.0
          : cardCurrency == baseCurrencyCode
            ? incomeToBaseRate
            : userRate * (getCurrencyRate(from: baseCurrencyCode, to: cardCurrency) ?? 1.0)
      } else {
        incomeToCardRate =
          userRate * (getCurrencyRate(from: convertCurrencyCode, to: cardCurrency) ?? 1.0)
        incomeToBaseRate =
          userRate * (getCurrencyRate(from: convertCurrencyCode, to: baseCurrencyCode) ?? 1.0)
      }
    } else {
      incomeToBaseRate =
        convertCurrencyCode == baseCurrencyCode
        ? userRate
        : incomeCurrencyCode == baseCurrencyCode
          ? 1.0
          : userRate * (getCurrencyRate(from: convertCurrencyCode, to: baseCurrencyCode) ?? 1.0)
      incomeToCardRate = 1.0
    }
  }

  /// 计算转账汇率
  private func calculateTransferRates() {
    guard let cards = self.cards else { return }
    let userRate = Double(conversionRate) ?? 1.0
    let sourceCurrency = cards.first(where: { $0.id == selectedTransferSourceCardId })?.currency
    let destCurrency = cards.first(where: { $0.id == selectedTransferDestinationCardId })?.currency

    expenseToCardRate =
      sourceCurrency != nil
      ? (convertCurrencyCode == sourceCurrency
        ? userRate
        : transferCurrencyCode == sourceCurrency
          ? 1.0
          : userRate * (getCurrencyRate(from: convertCurrencyCode, to: sourceCurrency!) ?? 1.0))
      : 1.0

    incomeToCardRate =
      destCurrency != nil
      ? (convertCurrencyCode == destCurrency
        ? userRate
        : transferCurrencyCode == destCurrency
          ? 1.0
          : userRate * (getCurrencyRate(from: convertCurrencyCode, to: destCurrency!) ?? 1.0))
      : 1.0

    expenseToBaseRate =
      transferCurrencyCode == baseCurrencyCode
      ? 1.0
      : convertCurrencyCode == baseCurrencyCode
        ? userRate
        : userRate * (getCurrencyRate(from: convertCurrencyCode, to: baseCurrencyCode) ?? 1.0)

    incomeToBaseRate = expenseToBaseRate
  }

  /// 记录交易类型变更日志
  func logTransactionTypeChange(_ newType: TransactionType) {
    let title = newType == .expense ? "支出" : newType == .income ? "收入" : "转账"
    let selectedInfo: String
    switch newType {
    case .expense:
      selectedInfo =
        "卡片ID: \(selectedExpenseCardId?.uuidString ?? "未选择"), 主类别ID: \(selectedExpenseCategoryId ?? "未选择"), 子类别ID: \(selectedExpenseSubCategoryId ?? "未选择"), 货币: \(expenseCurrencyCode)"
    case .income:
      selectedInfo =
        "卡片ID: \(selectedIncomeCardId?.uuidString ?? "未选择"), 主类别ID: \(selectedIncomeCategoryId ?? "未选择"), 子类别ID: \(selectedIncomeSubCategoryId ?? "未选择"), 货币: \(incomeCurrencyCode)"
    case .transfer:
      selectedInfo =
        "转出卡片ID: \(selectedTransferSourceCardId?.uuidString ?? "未选择"), 转入卡片ID: \(selectedTransferDestinationCardId?.uuidString ?? "未选择"), 转出货币: \(transferCurrencyCode), 转入货币: \(transferCurrencyCode)"
    case .refund, .createCard, .adjustCard:
      selectedInfo = "其他类型"
    }
    print("已切换到【\(title)】类型，\(selectedInfo)")
  }
}
