//
//  CardModel.swift
//  CStory
//
//  Created by NZUE on 2025/6/10.
//

import Foundation
import SwiftData

// MARK: - 卡片模型

/// 卡片数据模型
@Model
final class CardModel: Identifiable {
  /// 唯一标识符
  var id: UUID = UUID()
  /// 排序顺序
  var order: Int = 0
  /// 是否为信用卡
  var isCredit: Bool = false
  /// 是否可选择
  var isSelected: Bool = true
  /// 卡片名称
  var name: String = ""
  /// 备注信息
  var remark: String = ""
  /// 货币代码
  var currency: String = ""
  /// 货币符号
  var symbol: String = ""
  /// 余额
  var balance: Double = 0
  /// 信用额度
  var credit: Double = 0
  /// 是否纳入统计
  var isStatistics: Bool = false
  /// 封面图片名称
  var cover: String = ""
  /// 银行Logo数据
  var bankLogo: Data?
  /// 银行名称
  var bankName: String = ""
  /// 卡号（后四位）
  var cardNumber: String = ""
  /// 账单日（1-30，0表示月末，nil表示未设置）（仅信用卡使用）
  var billDay: Int?
  /// 还款类型（true: 固定日期，false: 账单日后X天）（仅信用卡使用）
  var isFixedDueDay: Bool = true
  /// 还款日（仅信用卡使用）
  var dueDay: Int?
  /// 创建时间
  var createdAt: Date = Date()
  /// 更新时间
  var updatedAt: Date = Date()
  init(
    id: UUID, order: Int, isCredit: Bool, isSelected: Bool, name: String, remark: String,
    currency: String, symbol: String, balance: Double, credit: Double, isStatistics: Bool,
    cover: String, bankLogo: Data? = nil, bankName: String, cardNumber: String, billDay: Int? = nil,
    isFixedDueDay: Bool, dueDay: Int? = nil, createdAt: Date, updatedAt: Date
  ) {
    self.id = id
    self.order = order
    self.isCredit = isCredit
    self.isSelected = isSelected
    self.name = name
    self.remark = remark
    self.currency = currency
    self.symbol = symbol
    self.balance = balance
    self.credit = credit
    self.isStatistics = isStatistics
    self.cover = cover
    self.bankLogo = bankLogo
    self.bankName = bankName
    self.cardNumber = cardNumber
    self.billDay = billDay
    self.isFixedDueDay = isFixedDueDay
    self.dueDay = dueDay

    self.createdAt = createdAt
    self.updatedAt = updatedAt
  }
}

// MARK: - 日期计算

extension CardModel {
  /// 获取下一个账单日期（仅适用于信用卡）
  func getNextBillDate() -> Date? {
    guard isCredit, let billDay = billDay else { return nil }
    
    // 使用DateHelperService统一处理账单日计算
    let isMonthEnd = (billDay == 0)
    let actualBillDay = isMonthEnd ? 31 : billDay  // DateHelperService会处理超过当月天数的情况
    
    return DateHelperService.shared.calculateNextBillDate(
      billDay: actualBillDay,
      from: Date(),
      isMonthEnd: isMonthEnd
    )
  }

  /// 获取下一个还款日期（仅适用于信用卡）
  func getNextDueDate() -> Date? {
    guard isCredit, let billDay = billDay, let dueDay = dueDay else { return nil }
    
    // 使用DateHelperService统一处理还款日计算
    let isMonthEnd = (billDay == 0)
    let actualBillDay = isMonthEnd ? 31 : billDay
    let dueMode: CreditCardDueMode = isFixedDueDay ? .fixed : .afterBill
    
    return DateHelperService.shared.calculateNextDueDate(
      billDay: actualBillDay,
      dueMode: dueMode,
      dueDay: dueDay,
      from: Date(),
      isMonthEnd: isMonthEnd
    )
  }
}

// MARK: - 时间戳管理

extension CardModel {
  /// 更新时间戳
  func updateTimestamp() {
    updatedAt = Date()
  }
}
