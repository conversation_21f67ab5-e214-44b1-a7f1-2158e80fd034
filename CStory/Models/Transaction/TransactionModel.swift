//
//  TransactionModel.swift
//  CStory
//
//  Created by NZUE on 2025/6/10.
//

import Foundation
import SwiftData

// MARK: - 交易模型

/// 交易数据模型
@Model
final class TransactionModel: Identifiable {
  /// 唯一标识符
  var id: UUID = UUID()
  /// 原始交易ID（用于退款关联）
  var originalTransactionId: UUID?
  /// 交易类型
  var transactionType: TransactionType = TransactionType.income
  /// 交易分类ID
  var transactionCategoryId: String?
  /// 转出卡片ID
  var fromCardId: UUID?
  /// 转入卡片ID
  var toCardId: UUID?
  /// 折扣金额
  var discountAmount: Double?
  /// 交易金额
  var transactionAmount: Double = 0
  /// 退款金额
  var refundAmount: Double?
  /// 货币代码
  var currency: String = ""
  /// 货币符号
  var symbol: String = ""
  /// 支出货币到卡片货币汇率
  var expenseToCardRate: Double = 1.0
  /// 支出货币到本位币汇率
  var expenseToBaseRate: Double = 1.0
  /// 收入货币到卡片货币汇率
  var incomeToCardRate: Double = 1.0
  /// 收入货币到本位币汇率
  var incomeToBaseRate: Double = 1.0
  /// 是否纳入统计
  var isStatistics: Bool = false
  /// 备注信息
  var remark: String = ""
  /// 原始交易ID（备用字段）
  var originalTradId: UUID?
  /// 交易日期
  var transactionDate: Date = Date()
  /// 创建时间
  var createdAt: Date = Date()
  /// 更新时间
  var updatedAt: Date = Date()
  init(
    id: UUID, originalTransactionId: UUID? = nil, transactionType: TransactionType, transactionCategoryId: String? = nil,
    fromCardId: UUID? = nil, toCardId: UUID? = nil, discountAmount: Double? = nil,
    transactionAmount: Double, refundAmount: Double? = nil, currency: String, symbol: String,
    expenseToCardRate: Double, expenseToBaseRate: Double, incomeToCardRate: Double,
    incomeToBaseRate: Double, isStatistics: Bool, remark: String, originalTradId: UUID? = nil,
    transactionDate: Date, createdAt: Date, updatedAt: Date
  ) {
    self.id = id
    self.originalTransactionId = originalTransactionId
    self.transactionType = transactionType
    self.transactionCategoryId = transactionCategoryId
    self.fromCardId = fromCardId
    self.toCardId = toCardId
    self.discountAmount = discountAmount
    self.transactionAmount = transactionAmount
    self.refundAmount = refundAmount
    self.currency = currency
    self.symbol = symbol
    self.expenseToCardRate = expenseToCardRate
    self.expenseToBaseRate = expenseToBaseRate
    self.incomeToCardRate = incomeToCardRate
    self.incomeToBaseRate = incomeToBaseRate
    self.isStatistics = isStatistics
    self.remark = remark
    self.originalTradId = originalTradId
    self.transactionDate = transactionDate
    self.createdAt = createdAt
    self.updatedAt = updatedAt
  }
}
