//
//  NumberFormatService.swift
//  CStory
//
//  Created by <PERSON> on `date`
//

import Foundation

/// 数字格式化服务
///
/// 统一的数字格式化服务，提供应用中所有数字格式化需求的解决方案。
/// 整合了货币显示、汇率显示、百分比显示、大数字显示等各种格式化功能。
/// 使用单例模式，确保性能和一致性。
final class NumberFormatService {

  /// 单例实例
  static let shared = NumberFormatService()

  /// 私有初始化器，确保单例模式
  private init() {}

  // MARK: - Core Formatting

  /// 格式化基础金额（带千分位分隔符）
  ///
  /// 将数字格式化为标准的金额显示格式，自动处理小数位数和千分位分隔符。
  ///
  /// - Parameters:
  ///   - amount: 要格式化的金额
  ///   - maxDecimals: 最大小数位数，默认为2
  ///   - minDecimals: 最小小数位数，默认为0
  ///   - useGrouping: 是否使用千分位分隔符，默认为true
  /// - Returns: 格式化后的金额字符串，如 "1,234.56"、"1,000"
  ///
  /// ## 示例
  /// ```swift
  /// NumberFormatService.formatAmount(1234.56)  // "1,234.56"
  /// NumberFormatService.formatAmount(1000.00)  // "1,000"
  /// NumberFormatService.formatAmount(999.1, maxDecimals: 1)  // "999.1"
  /// ```
  func formatAmount(
    _ amount: Double,
    maxDecimals: Int = 2,
    minDecimals: Int = 0,
    useGrouping: Bool = true
  ) -> String {
    let formatter = NumberFormatter()
    formatter.numberStyle = .decimal
    formatter.maximumFractionDigits = maxDecimals
    formatter.minimumFractionDigits = minDecimals
    formatter.usesGroupingSeparator = useGrouping

    return formatter.string(from: NSNumber(value: amount)) ?? String(amount)
  }

  /// 格式化金额用于输入框
  ///
  /// 将数字格式化为适合输入框显示的格式，不带千分位分隔符，便于编辑。
  ///
  /// - Parameters:
  ///   - amount: 要格式化的金额
  ///   - maxDecimals: 最大小数位数，默认为2
  /// - Returns: 不带千分位分隔符的金额字符串，如 "1234.56"
  func formatAmountForInput(
    _ amount: Double,
    maxDecimals: Int = 2
  ) -> String {
    return formatAmount(amount, maxDecimals: maxDecimals, minDecimals: 0, useGrouping: false)
  }

  // MARK: - Currency Formatting

  /// 格式化货币金额（带货币符号）
  ///
  /// 将金额格式化为完整的货币显示格式，包含货币符号、正负号和格式化的数字。
  ///
  /// - Parameters:
  ///   - amount: 金额数值
  ///   - symbol: 货币符号（如 "¥"、"$"、"€"）
  ///   - showDecimals: 是否显示小数部分，默认为true
  ///   - showPlusSign: 是否为正数显示加号，默认为false
  /// - Returns: 完整的货币字符串，如 "¥1,234.56"、"-$100"、"+€50"
  ///
  /// ## 示例
  /// ```swift
  /// NumberFormatService.formatCurrency(1234.56, symbol: "¥")     // "¥1,234.56"
  /// NumberFormatService.formatCurrency(-100, symbol: "$")        // "-$100"
  /// NumberFormatService.formatCurrency(50, symbol: "€", showPlusSign: true) // "+€50"
  /// ```
  func formatCurrency(
    _ amount: Double,
    symbol: String,
    showDecimals: Bool = true,
    showPlusSign: Bool = false
  ) -> String {
    let maxDecimals = showDecimals ? 2 : 0
    let formattedNumber = formatAmount(abs(amount), maxDecimals: maxDecimals)

    let sign: String
    if amount < 0 {
      sign = "-"
    } else if showPlusSign && amount > 0 {
      sign = "+"
    } else {
      sign = ""
    }

    return "\(sign)\(symbol)\(formattedNumber)"
  }

  /// 格式化分段货币显示的整数部分
  ///
  /// 专门用于DisplayCurrencyView等分段显示组件，格式化金额的整数部分。
  ///
  /// - Parameter amount: 原始金额
  /// - Returns: 格式化的整数部分字符串
  func formatCurrencyInteger(_ amount: Double) -> String {
    return formatAmount(floor(abs(amount)), maxDecimals: 0, minDecimals: 0, useGrouping: true)
  }

  /// 格式化分段货币显示的小数部分
  ///
  /// 专门用于DisplayCurrencyView等分段显示组件，格式化金额的小数部分。
  /// 返回带小数点的小数部分，如果没有小数则返回空字符串。
  ///
  /// - Parameter amount: 原始金额
  /// - Returns: 格式化的小数部分字符串（如 ".56"），整数则返回空字符串
  func formatCurrencyDecimal(_ amount: Double) -> String {
    // 如果金额为整数，返回空字符串
    if amount == floor(amount) {
      return ""
    }

    let decimalValue = abs(amount) - floor(abs(amount))
    let formatter = NumberFormatter()
    formatter.minimumFractionDigits = 0
    formatter.maximumFractionDigits = 2

    if let formatted = formatter.string(from: NSNumber(value: decimalValue)) {
      if formatted == "0" {
        return ""
      } else {
        // 去除前导0并添加小数点
        return "." + formatted.replacingOccurrences(of: "0.", with: "")
      }
    }

    return ""
  }

  // MARK: - Exchange Rate Formatting

  /// 格式化汇率显示
  ///
  /// 将汇率数值格式化为适合显示的字符串，自动处理小数位数和尾随零。
  /// 专门优化用于汇率显示场景，确保精度和可读性。
  ///
  /// - Parameters:
  ///   - rate: 汇率数值
  ///   - maxDecimals: 最大小数位数，默认为6
  ///   - removeTrailingZeros: 是否移除尾随零，默认为true
  /// - Returns: 格式化的汇率字符串
  ///
  /// ## 示例
  /// ```swift
  /// NumberFormatService.formatExchangeRate(1.234567)  // "1.234567"
  /// NumberFormatService.formatExchangeRate(1.200000)  // "1.2"
  /// NumberFormatService.formatExchangeRate(1.00)      // "1"
  /// ```
  func formatExchangeRate(
    _ rate: Double,
    maxDecimals: Int = 6,
    removeTrailingZeros: Bool = true
  ) -> String {
    let formatter = NumberFormatter()
    formatter.numberStyle = .decimal
    formatter.minimumFractionDigits = 0
    formatter.maximumFractionDigits = maxDecimals
    formatter.usesGroupingSeparator = false

    guard let formatted = formatter.string(from: NSNumber(value: rate)) else {
      return String(rate)
    }

    return removeTrailingZeros ? self.removeTrailingZeros(from: formatted) : formatted
  }

  // MARK: - Percentage Formatting

  /// 格式化百分比显示
  ///
  /// 将小数值格式化为百分比格式。
  ///
  /// - Parameters:
  ///   - value: 小数值（0.15 表示 15%）
  ///   - decimalPlaces: 小数位数，默认为0
  /// - Returns: 格式化的百分比字符串
  ///
  /// ## 示例
  /// ```swift
  /// NumberFormatService.formatPercentage(0.1534)                    // "15%"
  /// NumberFormatService.formatPercentage(0.1534, decimalPlaces: 2)  // "15.34%"
  /// ```
  func formatPercentage(
    _ value: Double,
    decimalPlaces: Int = 0
  ) -> String {
    let formatter = NumberFormatter()
    formatter.numberStyle = .percent
    formatter.minimumFractionDigits = decimalPlaces
    formatter.maximumFractionDigits = decimalPlaces

    return formatter.string(from: NSNumber(value: value)) ?? "\(value * 100)%"
  }

  // MARK: - Large Number Formatting

  /// 格式化大数字（使用 K、M、B 等单位）
  ///
  /// 将大数字格式化为简短的表示形式，使用国际通用的K、M、B后缀。
  ///
  /// - Parameter number: 要格式化的数字
  /// - Returns: 格式化后的字符串
  ///
  /// ## 示例
  /// ```swift
  /// NumberFormatService.formatLargeNumber(1500)      // "1.5K"
  /// NumberFormatService.formatLargeNumber(1500000)   // "1.5M"
  /// NumberFormatService.formatLargeNumber(999)       // "999"
  /// ```
  func formatLargeNumber(_ number: Double) -> String {
    let absNumber = abs(number)
    let sign = number < 0 ? "-" : ""

    if absNumber < 1000 {
      return formatAmount(number)
    } else if absNumber < 1_000_000 {
      let value = absNumber / 1000
      return "\(sign)\(formatAmount(value))K"
    } else if absNumber < 1_000_000_000 {
      let value = absNumber / 1_000_000
      return "\(sign)\(formatAmount(value))M"
    } else {
      let value = absNumber / 1_000_000_000
      return "\(sign)\(formatAmount(value))B"
    }
  }

  // MARK: - Expression & Calculation Formatting

  /// 格式化计算结果
  ///
  /// 专门用于表达式计算器和数学运算结果的格式化。
  /// 优化了精度处理和显示效果。
  ///
  /// - Parameters:
  ///   - result: 计算结果
  ///   - maxDecimals: 最大小数位数，默认为2
  /// - Returns: 格式化的结果字符串
  func formatCalculationResult(
    _ result: Double,
    maxDecimals: Int = 2
  ) -> String {
    let formatter = NumberFormatter()
    formatter.numberStyle = .decimal
    formatter.maximumFractionDigits = maxDecimals
    formatter.minimumFractionDigits = 0
    formatter.usesGroupingSeparator = true

    return formatter.string(from: NSNumber(value: result)) ?? String(result)
  }

  // MARK: - Utility Functions

  /// 移除字符串末尾的零
  ///
  /// 移除数字字符串末尾不必要的零和小数点，优化显示效果。
  ///
  /// - Parameter string: 原始字符串
  /// - Returns: 移除尾随零后的字符串
  ///
  /// ## 示例
  /// ```swift
  /// NumberFormatService.removeTrailingZeros(from: "1.200")  // "1.2"
  /// NumberFormatService.removeTrailingZeros(from: "1.000")  // "1"
  /// ```
  func removeTrailingZeros(from string: String) -> String {
    guard string.contains(".") else { return string }

    var result = string
    while result.hasSuffix("0") && result != "0" {
      result.removeLast()
    }
    if result.hasSuffix(".") {
      result.removeLast()
    }
    return result
  }

  /// 解析格式化的金额字符串
  ///
  /// 将带有千分位分隔符的字符串转换为数字。
  ///
  /// - Parameter string: 格式化后的金额字符串
  /// - Returns: 解析后的数字，解析失败返回 nil
  ///
  /// ## 示例
  /// ```swift
  /// NumberFormatService.parseAmount("1,234.56")  // 1234.56
  /// NumberFormatService.parseAmount("1234")      // 1234.0
  /// NumberFormatService.parseAmount("abc")       // nil
  /// ```
  func parseAmount(_ string: String) -> Double? {
    let formatter = NumberFormatter()
    formatter.numberStyle = .decimal
    return formatter.number(from: string)?.doubleValue
  }
}
