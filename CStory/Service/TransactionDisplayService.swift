//
//  TransactionDisplayService.swift
//  CStory
//
//  Created by <PERSON> on `date`
//

import Foundation
import SwiftUI

/// 交易显示服务
///
/// 统一的交易显示逻辑服务，提供所有交易相关的显示格式化功能。
/// 整合了交易金额显示、分类名称格式化、时间格式化、状态标签等功能。
/// 消除了跨多个ViewModel和组件的代码重复，确保显示逻辑的一致性。
final class TransactionDisplayService {

  /// 单例实例
  static let shared = TransactionDisplayService()

  /// 私有初始化器，确保单例模式
  private init() {}

  // MARK: - Amount Display

  /// 计算交易显示金额
  ///
  /// 根据交易类型和上下文计算用于显示的金额，处理正负号逻辑。
  /// 统一了不同组件中的金额显示计算逻辑。
  ///
  /// - Parameters:
  ///   - transaction: 交易模型
  ///   - context: 显示上下文（列表/详情/卡片等）
  ///   - relatedCard: 相关卡片（用于转账逻辑判断）
  /// - Returns: 用于显示的金额（含正负号）
  ///
  /// ## 显示规则
  /// - 支出交易：显示负数
  /// - 收入交易：显示正数
  /// - 转账交易：根据上下文和相关卡片显示
  /// - 退款交易：显示正数
  func calculateDisplayAmount(
    for transaction: TransactionModel,
    context: TransactionDisplayContext = .list,
    relatedCard: CardModel? = nil
  ) -> Double {
    let actualAmount = transaction.transactionAmount - (transaction.discountAmount ?? 0)

    switch transaction.transactionType {
    case .expense:
      return -abs(actualAmount)

    case .income:
      return abs(actualAmount)

    case .transfer:
      // 转账显示根据上下文决定
      switch context {
      case .fromCard:
        return -abs(actualAmount)  // 从转出卡片视角看是支出
      case .toCard:
        return abs(actualAmount)  // 从转入卡片视角看是收入
      case .cardDetail:
        // 卡片详情页：根据卡片角色显示正负号
        if let card = relatedCard {
          if transaction.fromCardId == card.id {
            return -abs(actualAmount)  // 当前卡片是转出方，显示负数
          } else if transaction.toCardId == card.id {
            return abs(actualAmount)  // 当前卡片是转入方，显示正数
          }
        }
        return abs(actualAmount)  // 无法判断卡片角色时，显示正数
      case .list, .detail:
        return abs(actualAmount)  // 列表和详情默认显示正数
      }

    case .refund:
      return abs(actualAmount)

    case .createCard, .adjustCard:
      // 系统交易根据实际金额符号显示
      return actualAmount
    }
  }

  /// 获取交易金额显示颜色
  ///
  /// 根据交易类型和金额确定显示颜色。
  ///
  /// - Parameters:
  ///   - transaction: 交易模型
  ///   - amount: 显示金额（可选，如不提供则自动计算）
  ///   - context: 显示上下文
  ///   - relatedCard: 相关卡片（用于转账逻辑判断）
  /// - Returns: 金额显示颜色
  func getAmountColor(
    for transaction: TransactionModel,
    amount: Double? = nil,
    context: TransactionDisplayContext = .list,
    relatedCard: CardModel? = nil
  ) -> Color {
    let displayAmount =
      amount ?? calculateDisplayAmount(for: transaction, context: context, relatedCard: relatedCard)

    switch transaction.transactionType {
    case .expense:
      return .cAccentRed

    case .income, .refund:
      return .cAccentGreen

    case .transfer:
      // 转账颜色根据上下文
      switch context {
      case .fromCard:
        return .cAccentRed  // 转出显示红色
      case .toCard:
        return .cAccentGreen  // 转入显示绿色
      case .cardDetail:
        return .cAccentBlue  // 卡片详情页保持蓝色
      case .list, .detail:
        return .gray  // 列表页使用灰色
      }

    case .createCard, .adjustCard:
      // 系统交易根据金额正负
      return displayAmount >= 0 ? .cAccentGreen : .cAccentRed
    }
  }

  /// 判断是否显示加号
  ///
  /// 根据交易类型和上下文判断是否为正数显示加号。
  ///
  /// - Parameters:
  ///   - transaction: 交易模型
  ///   - context: 显示上下文
  ///   - relatedCard: 相关卡片（用于转账逻辑判断）
  /// - Returns: 是否显示加号
  func shouldShowPlusSign(
    for transaction: TransactionModel,
    context: TransactionDisplayContext = .list,
    relatedCard: CardModel? = nil
  ) -> Bool {
    let displayAmount = calculateDisplayAmount(
      for: transaction, context: context, relatedCard: relatedCard)

    // 负数和零永远不显示加号
    if displayAmount <= 0 {
      return false
    }

    // 只有收入类交易的正数显示加号
    switch transaction.transactionType {
    case .income, .refund:
      return displayAmount > 0

    case .transfer:
      // 转账根据上下文决定是否显示加号
      switch context {
      case .toCard, .cardDetail:
        return displayAmount > 0  // 转入方或卡片详情页显示加号
      case .fromCard, .list, .detail:
        return false  // 转出方或列表页不显示加号
      }

    case .expense, .createCard, .adjustCard:
      return false
    }
  }

  /// 计算原始金额（用于删除线显示）
  ///
  /// 当有折扣时，计算原始金额用于删除线显示。
  ///
  /// - Parameter transaction: 交易模型
  /// - Returns: 原始金额，如果没有折扣则返回nil
  func calculateOriginalAmount(for transaction: TransactionModel) -> Double? {
    guard let discountAmount = transaction.discountAmount, discountAmount > 0 else {
      return nil
    }

    return transaction.transactionAmount
  }

  // MARK: - Category Display

  /// 获取交易分类显示名称
  ///
  /// 统一的分类名称格式化逻辑，支持主分类-子分类格式。
  ///
  /// - Parameters:
  ///   - transaction: 交易模型
  ///   - mainCategories: 主分类列表
  ///   - subCategories: 子分类列表
  ///   - format: 显示格式
  /// - Returns: 格式化的分类名称
  func getCategoryDisplayName(
    for transaction: TransactionModel,
    mainCategories: [TransactionMainCategoryModel],
    subCategories: [TransactionSubCategoryModel],
    format: CategoryDisplayFormat = .fullName
  ) -> String {
    guard let categoryId = transaction.transactionCategoryId else {
      return "未分类"
    }

    // 先尝试在子分类中查找
    if let subCategory = subCategories.first(where: { $0.id == categoryId }) {
      let mainCategory = mainCategories.first(where: { $0.id == subCategory.mainId })

      switch format {
      case .fullName:
        if let main = mainCategory {
          return "\(main.name)-\(subCategory.name)"
        } else {
          return subCategory.name
        }
      case .subOnly:
        return subCategory.name
      case .mainOnly:
        return mainCategory?.name ?? subCategory.name
      }
    }

    // 在主分类中查找
    if let mainCategory = mainCategories.first(where: { $0.id == categoryId }) {
      return mainCategory.name
    }

    return "未知分类"
  }

  /// 获取交易分类图标
  ///
  /// 获取交易分类对应的图标信息。
  ///
  /// - Parameters:
  ///   - transaction: 交易模型
  ///   - mainCategories: 主分类列表
  ///   - subCategories: 子分类列表
  /// - Returns: 分类图标，优先返回子分类图标
  func getCategoryIcon(
    for transaction: TransactionModel,
    mainCategories: [TransactionMainCategoryModel],
    subCategories: [TransactionSubCategoryModel]
  ) -> IconType? {
    guard let categoryId = transaction.transactionCategoryId else {
      return nil
    }

    // 优先使用子分类图标
    if let subCategory = subCategories.first(where: { $0.id == categoryId }) {
      return subCategory.icon
    }

    // 使用主分类图标
    if let mainCategory = mainCategories.first(where: { $0.id == categoryId }) {
      return mainCategory.icon
    }

    return nil
  }

  /// 获取完整的分类信息
  ///
  /// 返回包含名称和图标的完整分类信息。
  ///
  /// - Parameters:
  ///   - transaction: 交易模型
  ///   - mainCategories: 主分类列表
  ///   - subCategories: 子分类列表
  /// - Returns: 分类信息元组
  func getCategoryInfo(
    for transaction: TransactionModel,
    mainCategories: [TransactionMainCategoryModel],
    subCategories: [TransactionSubCategoryModel]
  ) -> (name: String, icon: IconType?) {
    let name = getCategoryDisplayName(
      for: transaction,
      mainCategories: mainCategories,
      subCategories: subCategories
    )
    let icon = getCategoryIcon(
      for: transaction,
      mainCategories: mainCategories,
      subCategories: subCategories
    )

    return (name: name, icon: icon)
  }

  // MARK: - Time Display

  /// 格式化交易时间显示
  ///
  /// 统一的时间格式化逻辑，支持今天/昨天/具体日期的智能显示。
  ///
  /// - Parameters:
  ///   - date: 交易日期
  ///   - style: 显示样式
  /// - Returns: 格式化的时间字符串
  func formatTransactionTime(
    _ date: Date,
    style: TimeDisplayStyle = .smart
  ) -> String {
    let calendar = Calendar.current
    let now = Date()
    let formatter = DateFormatter()

    switch style {
    case .smart:
      if calendar.isDate(date, inSameDayAs: now) {
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
      } else if calendar.isDateInYesterday(date) {
        formatter.dateFormat = "'昨天' HH:mm"
        return formatter.string(from: date)
      } else if calendar.isDate(date, equalTo: now, toGranularity: .year) {
        formatter.dateFormat = "M月d日 HH:mm"
        return formatter.string(from: date)
      } else {
        formatter.dateFormat = "yyyy年M月d日 HH:mm"
        return formatter.string(from: date)
      }

    case .timeOnly:
      formatter.dateFormat = "HH:mm"
      return formatter.string(from: date)

    case .dateOnly:
      if calendar.isDate(date, inSameDayAs: now) {
        return "今天"
      } else if calendar.isDateInYesterday(date) {
        return "昨天"
      } else if calendar.isDate(date, equalTo: now, toGranularity: .year) {
        formatter.dateFormat = "M月d日"
        return formatter.string(from: date)
      } else {
        formatter.dateFormat = "yyyy年M月d日"
        return formatter.string(from: date)
      }

    case .full:
      formatter.dateFormat = "yyyy年M月d日 HH:mm:ss"
      return formatter.string(from: date)
    }
  }

  // MARK: - Status Tags

  /// 检查交易是否有退款标签
  ///
  /// - Parameter transaction: 交易模型
  /// - Returns: 是否显示退款标签
  func hasRefundTag(for transaction: TransactionModel) -> Bool {
    return (transaction.refundAmount ?? 0) > 0
  }

  /// 检查交易是否有折扣标签
  ///
  /// - Parameter transaction: 交易模型
  /// - Returns: 是否显示折扣标签
  func hasDiscountTag(for transaction: TransactionModel) -> Bool {
    return (transaction.discountAmount ?? 0) > 0
  }

  /// 获取交易状态标签列表
  ///
  /// 返回交易的所有状态标签信息。
  ///
  /// - Parameter transaction: 交易模型
  /// - Returns: 状态标签数组
  func getStatusTags(for transaction: TransactionModel) -> [TransactionStatusTag] {
    var tags: [TransactionStatusTag] = []

    if hasRefundTag(for: transaction) {
      tags.append(.refund)
    }

    if hasDiscountTag(for: transaction) {
      tags.append(.discount)
    }

    return tags
  }

  // MARK: - Card Information

  /// 获取交易相关卡片信息
  ///
  /// 根据交易类型获取相关的卡片信息和显示文本。
  ///
  /// - Parameters:
  ///   - transaction: 交易模型
  ///   - cards: 卡片列表
  ///   - context: 显示上下文
  /// - Returns: 卡片显示信息
  func getCardDisplayInfo(
    for transaction: TransactionModel,
    cards: [CardModel],
    context: TransactionDisplayContext = .list
  ) -> CardDisplayInfo {
    switch transaction.transactionType {
    case .expense:
      if let card = cards.first(where: { $0.id == transaction.fromCardId }) {
        return CardDisplayInfo(
          cardName: card.name,
          bankName: card.bankName,
          displayText: card.name,
          isCredit: card.isCredit
        )
      }

    case .income, .refund:
      if let card = cards.first(where: { $0.id == transaction.toCardId }) {
        return CardDisplayInfo(
          cardName: card.name,
          bankName: card.bankName,
          displayText: card.name,
          isCredit: card.isCredit
        )
      }

    case .transfer:
      let fromCard = cards.first(where: { $0.id == transaction.fromCardId })
      let toCard = cards.first(where: { $0.id == transaction.toCardId })

      switch context {
      case .fromCard:
        if let card = fromCard {
          return CardDisplayInfo(
            cardName: card.name,
            bankName: card.bankName,
            displayText: "转出：\(card.name)",
            isCredit: card.isCredit
          )
        }
      case .toCard:
        if let card = toCard {
          return CardDisplayInfo(
            cardName: card.name,
            bankName: card.bankName,
            displayText: "转入：\(card.name)",
            isCredit: card.isCredit
          )
        }
      case .cardDetail:
        // 卡片详情页面显示转账信息
        if let from = fromCard, let to = toCard {
          return CardDisplayInfo(
            cardName: "\(from.name) → \(to.name)",
            bankName: "",
            displayText: "\(from.name) → \(to.name)",
            isCredit: false
          )
        }
      case .list, .detail:
        if let from = fromCard, let to = toCard {
          return CardDisplayInfo(
            cardName: "\(from.name) → \(to.name)",
            bankName: "",
            displayText: "\(from.name) → \(to.name)",
            isCredit: false
          )
        }
      }

    case .createCard, .adjustCard:
      // 系统交易，显示相关卡片
      if let card = cards.first(where: { $0.id == transaction.fromCardId ?? transaction.toCardId })
      {
        return CardDisplayInfo(
          cardName: card.name,
          bankName: card.bankName,
          displayText: card.name,
          isCredit: card.isCredit
        )
      }
    }

    // 默认返回
    return CardDisplayInfo(
      cardName: "未知卡片",
      bankName: "",
      displayText: "未知卡片",
      isCredit: false
    )
  }

  // MARK: - Transaction Type Display

  /// 获取交易类型显示文本
  ///
  /// - Parameter transactionType: 交易类型
  /// - Returns: 本地化的交易类型文本
  func getTransactionTypeDisplayText(_ transactionType: TransactionType) -> String {
    switch transactionType {
    case .income:
      return "收入"
    case .expense:
      return "支出"
    case .transfer:
      return "转账"
    case .refund:
      return "退款"
    case .createCard:
      return "创建卡片"
    case .adjustCard:
      return "调整余额"
    }
  }

  /// 获取交易类型图标
  ///
  /// - Parameter transactionType: 交易类型
  /// - Returns: 对应的系统图标名称
  func getTransactionTypeIcon(_ transactionType: TransactionType) -> String {
    switch transactionType {
    case .income:
      return "plus.circle.fill"
    case .expense:
      return "minus.circle.fill"
    case .transfer:
      return "arrow.left.arrow.right.circle.fill"
    case .refund:
      return "return.left.circle.fill"
    case .createCard:
      return "creditcard.circle.fill"
    case .adjustCard:
      return "slider.horizontal.3.circle.fill"
    }
  }
}

// MARK: - Supporting Types

/// 交易显示上下文
enum TransactionDisplayContext {
  case list  // 列表显示
  case detail  // 详情显示
  case fromCard  // 从转出卡片视角
  case toCard  // 从转入卡片视角
  case cardDetail  // 卡片详情页面（兼容旧版本）
}

/// 分类显示格式
enum CategoryDisplayFormat {
  case fullName  // 完整名称：主分类-子分类
  case subOnly  // 仅显示子分类名称
  case mainOnly  // 仅显示主分类名称
}

/// 时间显示样式
enum TimeDisplayStyle {
  case smart  // 智能显示：今天/昨天/日期+时间
  case timeOnly  // 仅显示时间
  case dateOnly  // 仅显示日期
  case full  // 完整日期时间
}

/// 交易状态标签
enum TransactionStatusTag {
  case refund  // 退款标签
  case discount  // 折扣标签

  var displayText: String {
    switch self {
    case .refund:
      return "退款"
    case .discount:
      return "折扣"
    }
  }

  var color: Color {
    switch self {
    case .refund:
      return .orange
    case .discount:
      return .blue
    }
  }
}

/// 卡片显示信息
struct CardDisplayInfo {
  let cardName: String
  let bankName: String
  let displayText: String
  let isCredit: Bool
}
