//
//  DateHelperService.swift
//  CStory
//
//  Created by <PERSON> on `date`
//

import Foundation

/// 日期处理服务
///
/// 日期处理的统一服务，提供日期范围计算、账单周期计算、时间段比较等功能。
/// 补充和扩展了DateFormattingHelper的功能，专注于业务逻辑相关的日期计算。
/// 消除了跨多个ViewModel和组件的日期计算代码重复。
///
/// **依赖关系**: 该服务依赖DateFormattingHelper提供通用的日期格式化和基础计算功能。
final class DateHelperService {

  /// 单例实例
  static let shared = DateHelperService()

  /// 私有初始化器，确保单例模式
  private init() {}

  // MARK: - Date Range Calculations

  /// 获取指定日期的周范围
  ///
  /// 根据指定日期计算该日期所在周的开始和结束时间。
  /// 周从周一开始，到周日结束。
  ///
  /// - Parameter date: 指定日期
  /// - Returns: 周开始和结束日期的元组
  ///
  /// ## 示例
  /// ```swift
  /// let now = Date()
  /// let (weekStart, weekEnd) = DateHelperService.getWeekRange(for: now)
  /// // weekStart: 本周一00:00:00
  /// // weekEnd: 下周一00:00:00
  /// ```
  func getWeekRange(for date: Date) -> (start: Date, end: Date) {
    let calendar = Calendar.current
    let weekComponents = calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: date)

    guard let weekStart = calendar.date(from: weekComponents) else {
      return (date, date)
    }

    guard let weekEnd = calendar.date(byAdding: .weekOfYear, value: 1, to: weekStart) else {
      return (weekStart, weekStart)
    }

    return (weekStart, weekEnd)
  }

  /// 获取指定日期的月范围
  ///
  /// 根据指定日期计算该日期所在月的开始和结束时间。
  ///
  /// - Parameter date: 指定日期
  /// - Returns: 月开始和结束日期的元组
  ///
  /// ## 示例
  /// ```swift
  /// let now = Date()
  /// let (monthStart, monthEnd) = DateHelperService.getMonthRange(for: now)
  /// // monthStart: 本月1日00:00:00
  /// // monthEnd: 下月1日00:00:00
  /// ```
  func getMonthRange(for date: Date) -> (start: Date, end: Date) {
    let calendar = Calendar.current
    let components = calendar.dateComponents([.year, .month], from: date)

    guard let monthStart = calendar.date(from: components) else {
      return (date, date)
    }

    guard let monthEnd = calendar.date(byAdding: .month, value: 1, to: monthStart) else {
      return (monthStart, monthStart)
    }

    return (monthStart, monthEnd)
  }

  /// 获取指定日期的年范围
  ///
  /// 根据指定日期计算该日期所在年的开始和结束时间。
  ///
  /// - Parameter date: 指定日期
  /// - Returns: 年开始和结束日期的元组
  ///
  /// ## 示例
  /// ```swift
  /// let now = Date()
  /// let (yearStart, yearEnd) = DateHelperService.getYearRange(for: now)
  /// // yearStart: 本年1月1日00:00:00
  /// // yearEnd: 下年1月1日00:00:00
  /// ```
  func getYearRange(for date: Date) -> (start: Date, end: Date) {
    let calendar = Calendar.current
    let components = calendar.dateComponents([.year], from: date)

    guard let yearStart = calendar.date(from: components) else {
      return (date, date)
    }

    guard let yearEnd = calendar.date(byAdding: .year, value: 1, to: yearStart) else {
      return (yearStart, yearStart)
    }

    return (yearStart, yearEnd)
  }

  /// 获取最近N天的日期范围
  ///
  /// 从指定日期开始，向前推算N天的日期范围。
  ///
  /// - Parameters:
  ///   - days: 天数
  ///   - from: 结束日期，默认为当前日期
  /// - Returns: 开始和结束日期的元组
  ///
  /// ## 示例
  /// ```swift
  /// let (start, end) = DateHelperService.getRecentDaysRange(days: 7)
  /// // start: 7天前00:00:00
  /// // end: 今天结束时间
  /// ```
  func getRecentDaysRange(days: Int, from date: Date = Date()) -> (start: Date, end: Date) {
    let endOfDay = DateFormattingHelper.shared.startOfDay(
      for: DateFormattingHelper.shared.date(from: date, byAdding: 1, to: .day) ?? date)

    guard let startDate = DateFormattingHelper.shared.date(from: date, byAdding: -days, to: .day) else {
      return (date, endOfDay)
    }

    let startOfDay = DateFormattingHelper.shared.startOfDay(for: startDate)
    return (startOfDay, endOfDay)
  }

  // MARK: - Time Period Comparisons

  /// 判断两个日期是否在同一周
  ///
  /// - Parameters:
  ///   - date1: 第一个日期
  ///   - date2: 第二个日期
  /// - Returns: 是否在同一周
  func isSameWeek(date1: Date, date2: Date) -> Bool {
    let calendar = Calendar.current
    let components1 = calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: date1)
    let components2 = calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: date2)

    return components1.yearForWeekOfYear == components2.yearForWeekOfYear
      && components1.weekOfYear == components2.weekOfYear
  }

  /// 判断两个日期是否在同一月
  ///
  /// - Parameters:
  ///   - date1: 第一个日期
  ///   - date2: 第二个日期
  /// - Returns: 是否在同一月
  func isSameMonth(date1: Date, date2: Date) -> Bool {
    let calendar = Calendar.current
    let components1 = calendar.dateComponents([.year, .month], from: date1)
    let components2 = calendar.dateComponents([.year, .month], from: date2)

    return components1.year == components2.year && components1.month == components2.month
  }

  /// 判断两个日期是否在同一年
  ///
  /// - Parameters:
  ///   - date1: 第一个日期
  ///   - date2: 第二个日期
  /// - Returns: 是否在同一年
  func isSameYear(date1: Date, date2: Date) -> Bool {
    let calendar = Calendar.current
    return calendar.component(.year, from: date1) == calendar.component(.year, from: date2)
  }

  /// 判断指定日期是否为当前时间段
  ///
  /// 根据时间段类型判断指定日期是否为当前的周/月/年。
  ///
  /// - Parameters:
  ///   - date: 要检查的日期
  ///   - period: 时间段类型
  /// - Returns: 是否为当前时间段
  func isCurrentPeriod(date: Date, period: DatePeriodType) -> Bool {
    let now = Date()

    switch period {
    case .week:
      return isSameWeek(date1: date, date2: now)
    case .month:
      return isSameMonth(date1: date, date2: now)
    case .year:
      return isSameYear(date1: date, date2: now)
    }
  }

  // MARK: - Credit Card Bill Cycle Calculations

  /// 计算信用卡下一个账单日
  ///
  /// 根据账单日设置计算下一个账单日期，处理月末日期的特殊情况。
  ///
  /// - Parameters:
  ///   - billDay: 账单日（1-31）
  ///   - from: 基准日期，默认为当前日期
  ///   - isMonthEnd: 是否为月末账单日
  /// - Returns: 下一个账单日期
  ///
  /// ## 示例
  /// ```swift
  /// // 每月15日为账单日
  /// let nextBill = DateHelperService.calculateNextBillDate(billDay: 15)
  ///
  /// // 月末账单日
  /// let nextBill = DateHelperService.calculateNextBillDate(billDay: 31, isMonthEnd: true)
  /// ```
  func calculateNextBillDate(
    billDay: Int,
    from date: Date = Date(),
    isMonthEnd: Bool = false
  ) -> Date {
    let calendar = Calendar.current
    let today = date

    // 获取当前月份信息
    let currentComponents = calendar.dateComponents([.year, .month, .day], from: today)
    guard let currentYear = currentComponents.year,
      let currentMonth = currentComponents.month,
      currentComponents.day != nil
    else {
      return today
    }

    // 计算本月的账单日
    let thisMonthBillDay: Int
    if isMonthEnd {
      // 月末账单日：取当月最后一天
      let monthRange = calendar.range(of: .day, in: .month, for: today)!
      thisMonthBillDay = monthRange.count
    } else {
      // 固定日期账单日：如果账单日超过当月天数，则取当月最后一天
      let monthRange = calendar.range(of: .day, in: .month, for: today)!
      thisMonthBillDay = min(billDay, monthRange.count)
    }

    // 创建本月账单日期
    var thisMonthBillComponents = DateComponents()
    thisMonthBillComponents.year = currentYear
    thisMonthBillComponents.month = currentMonth
    thisMonthBillComponents.day = thisMonthBillDay
    thisMonthBillComponents.hour = 0
    thisMonthBillComponents.minute = 0
    thisMonthBillComponents.second = 0

    guard let thisMonthBillDate = calendar.date(from: thisMonthBillComponents) else {
      return today
    }

    // 如果今天还没到本月账单日，返回本月账单日
    if today < thisMonthBillDate {
      return thisMonthBillDate
    }

    // 否则计算下月账单日
    guard let nextMonth = calendar.date(byAdding: .month, value: 1, to: thisMonthBillDate) else {
      return thisMonthBillDate
    }

    if isMonthEnd {
      // 月末账单日：取下月最后一天
      let nextMonthRange = calendar.range(of: .day, in: .month, for: nextMonth)!
      var nextMonthBillComponents = calendar.dateComponents([.year, .month], from: nextMonth)
      nextMonthBillComponents.day = nextMonthRange.count
      nextMonthBillComponents.hour = 0
      nextMonthBillComponents.minute = 0
      nextMonthBillComponents.second = 0

      return calendar.date(from: nextMonthBillComponents) ?? nextMonth
    } else {
      // 固定日期账单日：处理月份天数不足的情况
      let nextMonthRange = calendar.range(of: .day, in: .month, for: nextMonth)!
      let nextMonthBillDay = min(billDay, nextMonthRange.count)

      var nextMonthBillComponents = calendar.dateComponents([.year, .month], from: nextMonth)
      nextMonthBillComponents.day = nextMonthBillDay
      nextMonthBillComponents.hour = 0
      nextMonthBillComponents.minute = 0
      nextMonthBillComponents.second = 0

      return calendar.date(from: nextMonthBillComponents) ?? nextMonth
    }
  }

  /// 计算信用卡下一个还款日
  ///
  /// 根据账单日和还款设置计算下一个还款日期。
  ///
  /// - Parameters:
  ///   - billDay: 账单日（1-31）
  ///   - dueMode: 还款日模式
  ///   - dueDay: 还款日（固定模式）或账单日后天数（相对模式）
  ///   - from: 基准日期，默认为当前日期
  ///   - isMonthEnd: 是否为月末账单日
  /// - Returns: 下一个还款日期
  func calculateNextDueDate(
    billDay: Int,
    dueMode: CreditCardDueMode,
    dueDay: Int,
    from date: Date = Date(),
    isMonthEnd: Bool = false
  ) -> Date {
    let calendar = Calendar.current

    switch dueMode {
    case .fixed:
      // 固定还款日模式：每月固定日期还款
      return calculateNextBillDate(billDay: dueDay, from: date, isMonthEnd: false)

    case .afterBill:
      // 账单日后模式：账单日后N天还款
      let nextBillDate = calculateNextBillDate(billDay: billDay, from: date, isMonthEnd: isMonthEnd)
      return calendar.date(byAdding: .day, value: dueDay, to: nextBillDate) ?? nextBillDate
    }
  }

  // MARK: - Calendar Component Utilities

  /// 提取日期的年月日组件
  ///
  /// - Parameter date: 输入日期
  /// - Returns: 年、月、日组件的元组
  func extractDateComponents(_ date: Date) -> (year: Int, month: Int, day: Int) {
    return DateFormattingHelper.shared.extractDateComponents(date)
  }

  /// 创建指定年月日的日期
  ///
  /// - Parameters:
  ///   - year: 年
  ///   - month: 月
  ///   - day: 日
  ///   - hour: 小时，默认为0
  ///   - minute: 分钟，默认为0
  ///   - second: 秒，默认为0
  /// - Returns: 创建的日期，失败返回nil
  func createDate(
    year: Int,
    month: Int,
    day: Int,
    hour: Int = 0,
    minute: Int = 0,
    second: Int = 0
  ) -> Date? {
    return DateFormattingHelper.shared.createDate(
      year: year,
      month: month,
      day: day,
      hour: hour,
      minute: minute,
      second: second
    )
  }

  /// 获取指定月份的天数
  ///
  /// - Parameters:
  ///   - year: 年
  ///   - month: 月
  /// - Returns: 该月的天数
  func daysInMonth(year: Int, month: Int) -> Int {
    return DateFormattingHelper.shared.daysInMonth(year: year, month: month)
  }

  /// 获取月份的最后一天日期
  ///
  /// - Parameter date: 输入日期
  /// - Returns: 该月最后一天的日期
  func lastDayOfMonth(for date: Date) -> Date {
    return DateFormattingHelper.shared.lastDayOfMonth(for: date)
  }

  // MARK: - Transaction Period Utilities

  /// 根据时间段类型获取日期范围
  ///
  /// 统一的日期范围获取方法，支持周、月、年三种时间段。
  ///
  /// - Parameters:
  ///   - period: 时间段类型
  ///   - date: 基准日期
  /// - Returns: 开始和结束日期的元组
  func getDateRange(for period: DatePeriodType, date: Date) -> (start: Date, end: Date) {
    switch period {
    case .week:
      return getWeekRange(for: date)
    case .month:
      return getMonthRange(for: date)
    case .year:
      return getYearRange(for: date)
    }
  }

  /// 获取时间段的显示文本
  ///
  /// - Parameters:
  ///   - period: 时间段类型
  ///   - date: 基准日期
  /// - Returns: 时间段的显示文本
  func getPeriodDisplayText(for period: DatePeriodType, date: Date) -> String {
    let _ = Calendar.current

    switch period {
    case .week:
      let (weekStart, _) = getWeekRange(for: date)
      if isSameWeek(date1: date, date2: Date()) {
        return "本周"
      } else {
        return DateFormattingHelper.shared.format(date: weekStart, with: "M月d日") + "当周"
      }

    case .month:
      if isSameMonth(date1: date, date2: Date()) {
        return "本月"
      } else {
        return DateFormattingHelper.shared.format(date: date, with: "yyyy年M月")
      }

    case .year:
      if isSameYear(date1: date, date2: Date()) {
        return "今年"
      } else {
        return DateFormattingHelper.shared.format(date: date, with: "yyyy年")
      }
    }
  }
}

// MARK: - Supporting Types

/// 日期时间段类型
enum DatePeriodType {
  case week  // 周
  case month  // 月
  case year  // 年
}

/// 信用卡还款日模式
enum CreditCardDueMode {
  case fixed  // 固定日期模式
  case afterBill  // 账单日后模式
}

// MARK: - Legacy Compatibility

extension DateHelperService {
  /// 兼容TimeControlVM的dateRange计算
  ///
  /// 保持与现有TimeControlVM的兼容性。
  @available(*, deprecated, message: "Use getDateRange(for:date:) instead")
  func legacyGetDateRange(
    selectedPeriod: String,
    currentDate: Date
  ) -> (start: Date, end: Date) {
    switch selectedPeriod {
    case "week":
      return getWeekRange(for: currentDate)
    case "month":
      return getMonthRange(for: currentDate)
    case "year":
      return getYearRange(for: currentDate)
    default:
      return getMonthRange(for: currentDate)
    }
  }
}
